import React, { useState, useEffect } from 'react';
import { Brain, BarChart3, FileText, Zap, AlertCircle, Settings, ExternalLink } from 'lucide-react';
import TextInput from './components/TextInput';
import ResultsDisplay from './components/ResultsDisplay';
import LoadingSpinner from './components/LoadingSpinner';
import SDGKeywordsSearch from './components/SDGKeywordsSearch';
import AdminLogin from './components/AdminLogin';
import AdminPanel from './components/AdminPanel';
import { classifyText } from './utils/api';
import { SDGResult } from './types/sdg';
import { SDGKeyword, supabase, getLastUpdatedDate } from './lib/supabase';

function App() {
  const [text, setText] = useState('');
  const [selectedModel, setSelectedModel] = useState('elsevier-sdg-multi');
  const [results, setResults] = useState<SDGResult[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAdminLogin, setShowAdminLogin] = useState(false);
  const [showAdminPanel, setShowAdminPanel] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Check authentication status on mount
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setIsAuthenticated(!!session);
      if (session) {
        setShowAdminPanel(true);
      }
    };

    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setIsAuthenticated(!!session);
      if (!session) {
        setShowAdminPanel(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  // Load last updated date on mount and refresh periodically
  useEffect(() => {
    const loadLastUpdated = async () => {
      try {
        console.log('🔄 Loading last updated date...');
        const date = await getLastUpdatedDate();
        console.log('📅 Last updated date from database:', date);
        setLastUpdated(date);
      } catch (error) {
        console.error('❌ Error loading last updated date:', error);
      }
    };

    // Load immediately
    loadLastUpdated();

    // Set up interval to refresh every 30 seconds when user is active
    const interval = setInterval(() => {
      if (!document.hidden) {
        loadLastUpdated();
      }
    }, 30000);

    // Also refresh when window becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        loadLastUpdated();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Refresh last updated date when admin panel operations occur
  const refreshLastUpdatedDate = async () => {
    try {
      console.log('🔄 Refreshing last updated date after admin operation...');
      const date = await getLastUpdatedDate();
      console.log('📅 Refreshed last updated date:', date);
      setLastUpdated(date);
    } catch (error) {
      console.error('❌ Error refreshing last updated date:', error);
    }
  };

  const handleClassify = async () => {
    if (!text.trim()) {
      setError('Please enter some text to classify.');
      return;
    }

    // Check word count
    const wordCount = text.trim().split(/\s+/).length;
    if (wordCount < 50) {
      setError('Please enter at least 50 words to ensure better classification results.');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResults(null);

    try {
      const classificationResults = await classifyText(text, selectedModel);
      setResults(classificationResults);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Classification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setText('');
    setResults(null);
    setError(null);
  };

  const handleKeywordSelect = (keyword: SDGKeyword) => {
    // Add the selected keyword to the text input with proper spacing
    const newText = text ? `${text} ${keyword.keyword}` : keyword.keyword;
    setText(newText);
  };

  const handleAdminLogin = () => {
    setShowAdminLogin(false);
    setShowAdminPanel(true);
    // Refresh the last updated date when admin logs in
    refreshLastUpdatedDate();
  };

  const handleAdminLogout = () => {
    setShowAdminPanel(false);
    setIsAuthenticated(false);
    // Refresh the last updated date when admin logs out
    refreshLastUpdatedDate();
  };

  const models = [
    {
      value: 'elsevier-sdg-multi',
      label: 'Elsevier SDG Multi-label (Recommended)',
      description: 'Fast, Elsevier definition of SDGs, 104 languages'
    },
    {
      value: 'aurora-sdg-multi',
      label: 'Aurora SDG Multi-label (Fast)',
      description: 'Fast, Aurora definition of SDGs, 104 languages, ideal for SDG badges'
    },
    {
      value: 'aurora-sdg',
      label: 'Aurora SDG (High Accuracy)',
      description: 'Slower, Aurora definition of SDGs, 104 languages, ideal for data science labelling with recommended cut-off values 98% probability and above'
    }
  ];

  const selectedModelInfo = models.find(m => m.value === selectedModel);

  // Show admin panel if authenticated
  if (showAdminPanel && isAuthenticated) {
    return <AdminPanel onLogout={handleAdminLogout} />;
  }

  // Format the last updated date for display
  const formatLastUpdatedDate = (date: Date | null) => {
    if (!date) {
      return 'Unknown';
    }
    
    try {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Unknown';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-blue-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
          <div className="text-center">
            {/* Logos and Title Row */}
            <div className="flex flex-col lg:flex-row items-center justify-center gap-4 lg:gap-8 mb-4 sm:mb-6">
              {/* Logos Section */}
              <div className="flex items-center gap-6">
                {/* HKUST Logo */}
                <div className="flex items-center">
                  <a 
                    href="https://hkust.edu.hk/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="transition-transform hover:scale-105"
                  >
                    <img 
                      src="https://libapps-au.s3-ap-southeast-2.amazonaws.com/accounts/213351/images/hkust-logo-transparent.png" 
                      alt="HKUST Logo" 
                      className="h-12 sm:h-16 w-auto object-contain"
                    />
                  </a>
                </div>
                
                {/* Separator */}
                <div className="w-px h-12 bg-gray-300"></div>
                
                {/* Library Logo */}
                <div className="flex items-center">
                  <a 
                    href="https://library.hkust.edu.hk/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="transition-transform hover:scale-105"
                  >
                    <img 
                      src="https://libapps-au.s3-ap-southeast-2.amazonaws.com/accounts/213351/images/library-logo-transparent.png" 
                      alt="HKUST Library Logo" 
                      className="h-12 sm:h-16 w-auto object-contain"
                    />
                  </a>
                </div>
              </div>
              
              {/* Title and Subtitle Section */}
              <div className="flex flex-col lg:flex-row lg:items-center lg:gap-2 text-center lg:text-left">
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent whitespace-nowrap leading-tight py-1">
                  SDG Keywords and Classifier
                </h1>
                <div className="hidden lg:block text-gray-400 text-2xl">|</div>
                <p className="text-sm sm:text-base lg:text-lg text-gray-600 mt-2 lg:mt-0 leading-relaxed">
                  Classify Text Based on the UN Sustainable Development Goals
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Layout - Stack vertically */}
      <div className="lg:hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 sm:py-6 space-y-6">
          {/* SDG Keywords Database - Mobile */}
          <SDGKeywordsSearch onKeywordSelect={handleKeywordSelect} />

          {/* Input Text Section - Mobile */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="w-5 h-5 text-blue-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Input Text</h2>
            </div>

            {/* Model Selection */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-3">
                <Zap className="w-5 h-5 text-blue-600" />
                <label className="text-sm font-semibold text-gray-700">
                  Select Classification Model
                </label>
              </div>
              <select
                value={selectedModel}
                onChange={(e) => setSelectedModel(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
              >
                {models.map((model) => (
                  <option key={model.value} value={model.value}>
                    {model.label}
                  </option>
                ))}
              </select>
              {selectedModelInfo && (
                <div className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <p className="text-sm text-blue-700 flex items-center gap-2">
                    <Brain className="w-4 h-4" />
                    {selectedModelInfo.description}
                  </p>
                </div>
              )}
              
              {/* OSDG Note */}
              <div className="mt-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                <p className="text-sm text-gray-600">
                  <strong>Additional Model:</strong> OSDG is an open source initiative that aims to integrate various existing attempts to classify research according to Sustainable Development Goals.{' '}
                  <a 
                    href="https://osdg.ai/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-700 underline inline-flex items-center gap-1"
                  >
                    Learn more
                    <ExternalLink className="w-3 h-3" />
                  </a>
                </p>
              </div>
            </div>

            <TextInput
              value={text}
              onChange={setText}
              onClassify={handleClassify}
              onClear={handleClear}
              isLoading={isLoading}
            />

            {error && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-700">
                  <AlertCircle className="w-5 h-5" />
                  <span className="font-medium">Error</span>
                </div>
                <p className="text-red-600 mt-1">{error}</p>
              </div>
            )}
          </div>

          {/* Classification Results Section - Mobile */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 min-h-96">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-green-100 rounded-lg">
                <BarChart3 className="w-5 h-5 text-green-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Classification Results</h2>
            </div>

            {isLoading && <LoadingSpinner model={selectedModel} />}

            {results && !isLoading && (
              <ResultsDisplay results={results} modelUsed={selectedModel} inputText={text} />
            )}

            {!results && !isLoading && (
              <div className="text-center py-12">
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="w-6 h-6 text-gray-400" />
                </div>
                <p className="text-gray-500 text-lg">
                  Enter text and click "Classify Text" to see SDG analysis results
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Desktop Layout - Two panels side by side */}
      <div className="hidden lg:block">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="grid grid-cols-12 gap-6 min-h-[calc(100vh-180px)]">
            {/* Left Panel - SDG Keywords Database (40% width) */}
            <div className="col-span-5 overflow-hidden">
              <div className="h-full overflow-y-auto">
                <SDGKeywordsSearch onKeywordSelect={handleKeywordSelect} />
              </div>
            </div>

            {/* Right Panel - Input and Results (60% width) */}
            <div className="col-span-7 flex flex-col gap-6 overflow-hidden">
              {/* Input Text Section */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 flex-shrink-0">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="w-6 h-6 text-blue-600" />
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-900">Input Text</h2>
                </div>

                {/* Model Selection */}
                <div className="mb-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Zap className="w-5 h-5 text-blue-600" />
                    <label className="text-sm font-semibold text-gray-700">
                      Select Classification Model
                    </label>
                  </div>
                  <select
                    value={selectedModel}
                    onChange={(e) => setSelectedModel(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
                  >
                    {models.map((model) => (
                      <option key={model.value} value={model.value}>
                        {model.label}
                      </option>
                    ))}
                  </select>
                  {selectedModelInfo && (
                    <div className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-100">
                      <p className="text-sm text-blue-700 flex items-center gap-2">
                        <Brain className="w-4 h-4" />
                        {selectedModelInfo.description}
                      </p>
                    </div>
                  )}
                  
                  {/* OSDG Note */}
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <p className="text-sm text-gray-600">
                      <strong>Additional Model:</strong> OSDG is an open source initiative that aims to integrate various existing attempts to classify research according to Sustainable Development Goals.{' '}
                      <a 
                        href="https://osdg.ai/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-700 underline inline-flex items-center gap-1"
                      >
                        Learn more
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    </p>
                  </div>
                </div>

                <TextInput
                  value={text}
                  onChange={setText}
                  onClassify={handleClassify}
                  onClear={handleClear}
                  isLoading={isLoading}
                />

                {error && (
                  <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center gap-2 text-red-700">
                      <AlertCircle className="w-5 h-5" />
                      <span className="font-medium">Error</span>
                    </div>
                    <p className="text-red-600 mt-1">{error}</p>
                  </div>
                )}
              </div>

              {/* Classification Results Section - Fixed height and scrollable */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 flex-1 flex flex-col overflow-hidden min-h-0">
                <div className="p-6 flex-shrink-0">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <BarChart3 className="w-6 h-6 text-green-600" />
                    </div>
                    <h2 className="text-2xl font-semibold text-gray-900">Classification Results</h2>
                  </div>
                </div>

                <div className="flex-1 overflow-y-auto px-6 pb-6">
                  {isLoading && <LoadingSpinner model={selectedModel} />}

                  {results && !isLoading && (
                    <ResultsDisplay results={results} modelUsed={selectedModel} inputText={text} />
                  )}

                  {!results && !isLoading && (
                    <div className="text-center py-16">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <BarChart3 className="w-8 h-8 text-gray-400" />
                      </div>
                      <p className="text-gray-500 text-lg">
                        Enter text and click "Classify Text" to see SDG analysis results
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white/80 backdrop-blur-sm border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="text-center">
            <p className="text-gray-600">
              Developed by HKUST Library © 2025 | Powered by the{' '}
              <a 
                href="https://aurora-universities.eu/sdg-research/classify/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-700 underline"
              >
                Aurora SDG Classifier API
              </a>
            </p>
            <div className="flex items-center justify-center gap-4 mt-2">
              <p className="text-sm text-gray-500">
                Keywords database last updated: {formatLastUpdatedDate(lastUpdated)}
              </p>
              {/* Admin Login Button */}
              <button
                onClick={() => setShowAdminLogin(true)}
                className="text-sm text-gray-400 hover:text-gray-600 transition-colors flex items-center gap-1"
                title="Admin Login"
              >
                <Settings className="w-4 h-4" />
                Admin
              </button>
            </div>
          </div>
        </div>
      </footer>

      {/* Admin Login Modal */}
      {showAdminLogin && (
        <AdminLogin
          onLoginSuccess={handleAdminLogin}
          onClose={() => setShowAdminLogin(false)}
        />
      )}
    </div>
  );
}

export default App;