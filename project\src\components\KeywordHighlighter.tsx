import React, { useState, useEffect, useMemo } from 'react';
import { SDGKeyword, SDG_NAMES, searchKeywords } from '../lib/supabase';
import { keywordExtractor, KeywordMatch } from '../utils/keywordExtractor';

interface KeywordHighlighterProps {
  text: string;
  className?: string;
}

const KeywordHighlighter: React.FC<KeywordHighlighterProps> = ({ 
  text, 
  className = '' 
}) => {
  const [hoveredKeyword, setHoveredKeyword] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [keywordsLoaded, setKeywordsLoaded] = useState(false);
  const [showUniqueKeywords, setShowUniqueKeywords] = useState(false);

  // Load keywords into the extractor when component mounts
  useEffect(() => {
    const loadKeywords = async () => {
      if (keywordsLoaded) return;
      
      setIsLoading(true);
      try {
        console.log('🔄 Loading keywords for FlashText extractor...');
        
        // Load keywords from all SDGs
        const allKeywords: SDGKeyword[] = [];
        const promises = [];
        
        for (let sdg = 1; sdg <= 17; sdg++) {
          promises.push(
            searchKeywords('', sdg.toString(), 'alphabetical').catch(err => {
              console.warn(`Failed to load keywords for SDG ${sdg}:`, err);
              return [];
            })
          );
        }
        
        const results = await Promise.all(promises);
        results.forEach(sdgKeywords => {
          allKeywords.push(...sdgKeywords);
        });
        
        console.log(`📚 Loaded ${allKeywords.length} keywords for extraction`);
        
        // Build the keyword extractor
        keywordExtractor.buildFromKeywords(allKeywords);
        setKeywordsLoaded(true);
        
      } catch (error) {
        console.error('❌ Error loading keywords for extractor:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadKeywords();
  }, [keywordsLoaded]);
  // Get SDG color for highlighting
  const getSDGColor = (sdg: string) => {
    const colors: { [key: string]: { bg: string; text: string; border: string } } = {
      '1': { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-300' },
      '2': { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-300' },
      '3': { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-300' },
      '4': { bg: 'bg-red-200', text: 'text-red-900', border: 'border-red-400' },
      '5': { bg: 'bg-pink-100', text: 'text-pink-800', border: 'border-pink-300' },
      '6': { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-300' },
      '7': { bg: 'bg-yellow-200', text: 'text-yellow-900', border: 'border-yellow-400' },
      '8': { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-300' },
      '9': { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-300' },
      '10': { bg: 'bg-pink-200', text: 'text-pink-900', border: 'border-pink-400' },
      '11': { bg: 'bg-orange-200', text: 'text-orange-900', border: 'border-orange-400' },
      '12': { bg: 'bg-amber-100', text: 'text-amber-800', border: 'border-amber-300' },
      '13': { bg: 'bg-emerald-100', text: 'text-emerald-800', border: 'border-emerald-300' },
      '14': { bg: 'bg-cyan-100', text: 'text-cyan-800', border: 'border-cyan-300' },
      '15': { bg: 'bg-lime-100', text: 'text-lime-800', border: 'border-lime-300' },
      '16': { bg: 'bg-indigo-100', text: 'text-indigo-800', border: 'border-indigo-300' },
      '17': { bg: 'bg-slate-100', text: 'text-slate-800', border: 'border-slate-300' }
    };
    return colors[sdg] || { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-300' };
  };

  // Extract keywords using FlashText-inspired algorithm
  const matchedKeywords = useMemo(() => {
    if (!text || !keywordsLoaded || isLoading) return [];
    
    console.log('🔍 Extracting keywords from text...');
    const startTime = performance.now();
    
    const matches = keywordExtractor.extractKeywords(text);
    
    const endTime = performance.now();
    console.log(`⚡ Keyword extraction completed in ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`🎯 Found ${matches.length} high-confidence keyword matches`);
    
    return matches;
  }, [text, keywordsLoaded, isLoading]);

  // Extract unique keywords from the input text itself
  const uniqueKeywords = useMemo(() => {
    if (!text || !keywordsLoaded) return [];
    
    console.log('🔍 Extracting unique keywords from input text...');
    const startTime = performance.now();
    
    const keywords = keywordExtractor.extractUniqueKeywords(text);
    
    const endTime = performance.now();
    console.log(`⚡ Unique keyword extraction completed in ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`📝 Found ${keywords.length} unique keywords/phrases`);
    
    return keywords;
  }, [text, keywordsLoaded]);

  // Render highlighted text
  const renderHighlightedText = () => {
    if (!matchedKeywords.length) {
      return <span>{text}</span>;
    }

    const elements: React.ReactNode[] = [];
    let lastIndex = 0;

    // Sort matches by position in text first, then by SDG number for consistent ordering
    const sortedMatches = [...matchedKeywords].sort((a, b) => {
      // First sort by position in text
      if (a.startIndex !== b.startIndex) {
        return a.startIndex - b.startIndex;
      }
      // Then by SDG number for consistent ordering when positions are the same
      return parseInt(a.sdg) - parseInt(b.sdg);
    });

    sortedMatches.forEach((match, index) => {
        const matchId = `${match.keyword}-${match.sdg}-${match.startIndex}`;
        const isHovered = hoveredKeyword === matchId;
      if (match.startIndex > lastIndex) {
        elements.push(
          <span key={`text-${index}`}>
            {text.substring(lastIndex, match.startIndex)}
          </span>
        );
      }

      // Add the highlighted match
      const colors = getSDGColor(match.sdg);
      
      elements.push(
        <span
          key={matchId}
          className={`
            relative inline-block px-2 py-1 rounded-lg border-2 cursor-pointer
            transition-all duration-200 transform
            ${colors.bg} ${colors.text} ${colors.border}
            ${isHovered ? 'scale-105 shadow-lg z-10 ring-2 ring-blue-300' : 'hover:scale-105 hover:shadow-md hover:ring-1 hover:ring-blue-200'}
          `}
          onMouseEnter={() => setHoveredKeyword(matchId)}
          onMouseLeave={() => setHoveredKeyword(null)}
          title={`SDG ${match.sdg}: ${SDG_NAMES[match.sdg]}`}
        >
          {text.substring(match.startIndex, match.endIndex)}
          
          {/* SDG Label */}
          <span className={`
            absolute -top-2 -right-2 text-xs font-bold px-2 py-1 rounded-full
            bg-white border-2 ${colors.border} ${colors.text}
            transition-all duration-200
            ${isHovered ? 'scale-110 shadow-md' : 'shadow-sm'}
          `}>
            {match.sdg}
          </span>
          
          {/* Confidence indicator */}
          {match.confidence > 0.8 && (
            <span className="absolute -top-1 -left-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white shadow-sm"></span>
          )}

          {/* Hover tooltip */}
          {isHovered && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-20">
              <div className="bg-gray-900 text-white text-sm rounded-lg px-4 py-3 whitespace-nowrap shadow-xl border border-gray-700">
                <div className="font-bold text-blue-300">SDG {match.sdg}</div>
                <div className="text-green-300 font-medium">Confidence: {(match.confidence * 100).toFixed(0)}%</div>
                <div className="text-gray-300 text-xs mt-1">{SDG_NAMES[match.sdg]}</div>
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>
          )}
        </span>
      );

      lastIndex = match.endIndex;
    });

    // Add remaining text
    if (lastIndex < text.length) {
      elements.push(
        <span key="text-end">
          {text.substring(lastIndex)}
        </span>
      );
    }

    return elements;
  };

  // Get statistics about matches
  const matchStats = useMemo(() => {
    const uniqueKeywords = new Set(matchedKeywords.map(m => m.keyword));
    const uniqueSDGs = new Set(matchedKeywords.map(m => m.sdg));
    
    // Count keywords per SDG for sizing
    const sdgKeywordCounts = matchedKeywords.reduce((acc, match) => {
      acc[match.sdg] = (acc[match.sdg] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });
    
    // Always ensure SDGs are sorted numerically for consistent display
    const sortedSDGs = Array.from(uniqueSDGs).sort((a, b) => parseInt(a) - parseInt(b));
    
    return {
      totalMatches: matchedKeywords.length,
      uniqueKeywords: uniqueKeywords.size,
      uniqueSDGs: uniqueSDGs.size,
      sdgBreakdown: sortedSDGs,
      sdgKeywordCounts,
      avgConfidence: matchedKeywords.length > 0 
        ? matchedKeywords.reduce((sum, m) => sum + m.confidence, 0) / matchedKeywords.length 
        : 0
    };
  }, [matchedKeywords]);

  // Function to get dynamic sizing based on keyword count
  const getSDGSizing = (sdg: string, count: number, maxCount: number) => {
    const ratio = count / maxCount;
    
    // Base size classes
    let sizeClasses = 'px-3 py-1 text-xs';
    let fontWeight = 'font-bold';
    let shadow = '';
    let transform = '';
    
    if (ratio >= 0.8) {
      // Very high frequency - largest size
      sizeClasses = 'px-4 py-2 text-base';
      fontWeight = 'font-black';
      shadow = 'shadow-lg';
      transform = 'scale-110';
    } else if (ratio >= 0.6) {
      // High frequency - large size
      sizeClasses = 'px-4 py-1.5 text-sm';
      fontWeight = 'font-black';
      shadow = 'shadow-md';
      transform = 'scale-105';
    } else if (ratio >= 0.4) {
      // Medium frequency - medium size
      sizeClasses = 'px-3 py-1.5 text-sm';
      fontWeight = 'font-bold';
      shadow = 'shadow-sm';
    } else if (ratio >= 0.2) {
      // Low-medium frequency - slightly larger
      sizeClasses = 'px-3 py-1 text-xs';
      fontWeight = 'font-bold';
    }
    // else: use default base size
    
    return {
      sizeClasses,
      fontWeight,
      shadow,
      transform,
      count
    };
  };
  return (
    <div className={`space-y-3 ${className}`}>
      {/* Loading indicator */}
      {isLoading && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center gap-2 text-blue-700">
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-sm font-medium">Loading keyword database...</span>
          </div>
        </div>
      )}


      {/* Statistics */}
      {matchedKeywords.length > 0 && keywordsLoaded && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200 shadow-sm">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-4 h-4 bg-green-500 rounded-full flex-shrink-0"></div>
            <h4 className="text-lg font-bold text-green-900">SDG Keywords Found</h4>
          </div>
          
          <div className="flex flex-wrap items-center gap-4 text-sm mb-3">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full flex-shrink-0"></div>
              <span className="font-medium text-green-900">
                {matchStats.totalMatches} keyword matches found
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full flex-shrink-0"></div>
              <span className="font-medium text-blue-900">
                {matchStats.uniqueKeywords} unique keywords
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-purple-500 rounded-full flex-shrink-0"></div>
              <span className="font-medium text-purple-900">
                {matchStats.uniqueSDGs} SDGs represented
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-yellow-500 rounded-full flex-shrink-0"></div>
              <span className="font-medium text-yellow-900">
                {(matchStats.avgConfidence * 100).toFixed(0)}% avg confidence
              </span>
            </div>
          </div>
          
          {/* SDG breakdown */}
          <div className="flex flex-wrap items-center gap-3">
            {matchStats.sdgBreakdown.map(sdg => {
              const colors = getSDGColor(sdg);
              const keywordCount = matchStats.sdgKeywordCounts[sdg] || 0;
              const maxCount = Math.max(...Object.values(matchStats.sdgKeywordCounts));
              const sizing = getSDGSizing(sdg, keywordCount, maxCount);
              
              return (
                <span
                  key={sdg}
                  className={`
                    inline-flex items-center gap-2 rounded-full border-2 transition-all duration-300
                    ${colors.bg} ${colors.text} ${colors.border} 
                    ${sizing.sizeClasses} ${sizing.fontWeight} ${sizing.shadow}
                    ${sizing.transform} hover:scale-110
                  `}
                  title={`SDG ${sdg}: ${keywordCount} keyword${keywordCount !== 1 ? 's' : ''} found`}
                >
                  <span>SDG {sdg}</span>
                  <span className={`
                    inline-flex items-center justify-center min-w-[1.5rem] h-6 
                    bg-white rounded-full text-xs font-black
                    ${colors.text} border ${colors.border}
                  `}>
                    {keywordCount}
                  </span>
                </span>
              );
            })}
          </div>
        </div>
      )}

      {/* Highlighted text */}
      <div className="relative bg-white rounded-xl p-5 border border-gray-200 shadow-sm" style={{ minHeight: keywordsLoaded ? 'auto' : '100px' }}>
        <div className="flex items-center gap-2 mb-4">
          <div className="w-4 h-4 bg-blue-500 rounded-full flex-shrink-0"></div>
          <h4 className="text-lg font-bold text-gray-900">Text with SDG Keywords Highlighted</h4>
        </div>
        <div className="text-base leading-relaxed whitespace-pre-wrap break-words bg-gray-50 rounded-lg p-4 border border-gray-200">
          {renderHighlightedText()}
        </div>
      </div>

      {/* No results message */}
      {text.trim() && matchedKeywords.length === 0 && keywordsLoaded && !isLoading && (
        <div className="bg-amber-50 border border-amber-200 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
              <span className="text-amber-600 text-lg">🔍</span>
            </div>
            <h4 className="text-lg font-bold text-amber-900">No SDG Keywords Found</h4>
          </div>
          <div className="text-amber-800">
            <p className="text-sm mb-2">
              No keywords from our SDG database were found in your text.
            </p>
            <p className="text-xs text-amber-700">
              💡 <strong>Tip:</strong> Try adding more descriptive terms related to sustainable development topics like health, education, environment, or social issues.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default KeywordHighlighter;