import React, { useState } from 'react';
import { Trophy, Info, ChevronDown, ChevronUp, BarChart3, Search, BookOpen, ExternalLink } from 'lucide-react';
import { SDGResult } from '../types/sdg';
import <PERSON><PERSON><PERSON> from './PieChart';
import SDGDetailsCard from './SDGDetailsCard';

interface ResultsDisplayProps {
  results: SDGResult[];
  modelUsed: string;
  inputText?: string;
}

const ResultsDisplay: React.FC<ResultsDisplayProps> = ({ results, modelUsed, inputText }) => {
  const [showAllResults, setShowAllResults] = useState(false);
  const [selectedSDG, setSelectedSDG] = useState<SDGResult | null>(null);
  
  // Handle empty results
  if (results.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Search className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-xl font-semibold text-gray-700 mb-2">No SDG Classifications Found</h3>
        <p className="text-gray-500 text-lg mb-4">
          The model couldn't identify any significant SDG relevance in the provided text.
        </p>
        <div className="bg-blue-50 rounded-lg p-4 max-w-md mx-auto">
          <p className="text-blue-700 text-sm">
            <strong>Tip:</strong> Try using more descriptive text or content that relates to specific 
            sustainable development topics like health, education, environment, or social issues.
          </p>
        </div>
      </div>
    );
  }

  const topResult = results[0];
  const significantResults = results.filter(result => result.score > 1);
  const topResults = results.slice(0, 5); // Show top 5 by default

  // SDG brief information data
  const getSDGBriefInfo = (code: string) => {
    const briefInfo: { [key: string]: { title: string; description: string; keyTasks: string[] } } = {
      '1': {
        title: 'No Poverty',
        description: 'End poverty in all its forms everywhere',
        keyTasks: [
          'Eradicate extreme poverty (less than $1.90 per day)',
          'Reduce poverty by at least 50% in all dimensions',
          'Implement social protection systems'
        ]
      },
      '2': {
        title: 'Zero Hunger',
        description: 'End hunger, achieve food security and improved nutrition, and promote sustainable agriculture',
        keyTasks: [
          'End hunger and ensure access to nutritious food',
          'End all forms of malnutrition',
          'Double agricultural productivity of small-scale farmers'
        ]
      },
      '3': {
        title: 'Good Health and Well-being',
        description: 'Ensure healthy lives and promote well-being for all at all ages',
        keyTasks: [
          'Reduce global maternal mortality',
          'End preventable deaths of children under 5',
          'End epidemics of AIDS, tuberculosis, malaria'
        ]
      },
      '4': {
        title: 'Quality Education',
        description: 'Ensure inclusive and equitable quality education and promote lifelong learning',
        keyTasks: [
          'Ensure all children complete free primary and secondary education',
          'Ensure equal access to quality early childhood development',
          'Eliminate gender disparities in education'
        ]
      },
      '5': {
        title: 'Gender Equality',
        description: 'Achieve gender equality and empower all women and girls',
        keyTasks: [
          'End discrimination against women and girls',
          'Eliminate violence against women and girls',
          'Eliminate harmful practices like child marriage'
        ]
      },
      '6': {
        title: 'Clean Water and Sanitation',
        description: 'Ensure availability and sustainable management of water and sanitation for all',
        keyTasks: [
          'Achieve universal access to safe drinking water',
          'Achieve access to adequate sanitation and hygiene',
          'Improve water quality and reduce pollution'
        ]
      },
      '7': {
        title: 'Affordable and Clean Energy',
        description: 'Ensure access to affordable, reliable, sustainable and modern energy for all',
        keyTasks: [
          'Ensure universal access to modern energy services',
          'Increase share of renewable energy',
          'Double the rate of improvement in energy efficiency'
        ]
      },
      '8': {
        title: 'Decent Work and Economic Growth',
        description: 'Promote sustained, inclusive and sustainable economic growth, full employment and decent work',
        keyTasks: [
          'Sustain per capita economic growth',
          'Achieve higher levels of economic productivity',
          'Promote policies that support job creation'
        ]
      },
      '9': {
        title: 'Industry, Innovation and Infrastructure',
        description: 'Build resilient infrastructure, promote inclusive industrialization and foster innovation',
        keyTasks: [
          'Develop quality, reliable and sustainable infrastructure',
          'Promote inclusive and sustainable industrialization',
          'Increase access to financial services and markets'
        ]
      },
      '10': {
        title: 'Reduced Inequalities',
        description: 'Reduce inequality within and among countries',
        keyTasks: [
          'Achieve and sustain income growth for bottom 40%',
          'Empower and promote social, economic and political inclusion',
          'Ensure equal opportunity and reduce outcome inequalities'
        ]
      },
      '11': {
        title: 'Sustainable Cities and Communities',
        description: 'Make cities and human settlements inclusive, safe, resilient and sustainable',
        keyTasks: [
          'Ensure access to adequate and affordable housing',
          'Provide access to safe and affordable transport systems',
          'Enhance inclusive and sustainable urbanization'
        ]
      },
      '12': {
        title: 'Responsible Consumption and Production',
        description: 'Ensure sustainable consumption and production patterns',
        keyTasks: [
          'Implement sustainable consumption and production framework',
          'Achieve sustainable management of natural resources',
          'Halve per capita global food waste'
        ]
      },
      '13': {
        title: 'Climate Action',
        description: 'Take urgent action to combat climate change and its impacts',
        keyTasks: [
          'Strengthen resilience and adaptive capacity to climate hazards',
          'Integrate climate change measures into policies',
          'Improve education and awareness on climate change'
        ]
      },
      '14': {
        title: 'Life Below Water',
        description: 'Conserve and sustainably use the oceans, seas and marine resources',
        keyTasks: [
          'Prevent and reduce marine pollution',
          'Sustainably manage and protect marine ecosystems',
          'Minimize ocean acidification'
        ]
      },
      '15': {
        title: 'Life on Land',
        description: 'Protect, restore and promote sustainable use of terrestrial ecosystems',
        keyTasks: [
          'Ensure conservation and restoration of terrestrial ecosystems',
          'Promote sustainable management of forests',
          'Combat desertification and restore degraded land'
        ]
      },
      '16': {
        title: 'Peace, Justice and Strong Institutions',
        description: 'Promote peaceful and inclusive societies, provide access to justice and build effective institutions',
        keyTasks: [
          'Significantly reduce all forms of violence',
          'End abuse, exploitation, trafficking and torture of children',
          'Promote rule of law and ensure equal access to justice'
        ]
      },
      '17': {
        title: 'Partnerships for the Goals',
        description: 'Strengthen the means of implementation and revitalize the global partnership',
        keyTasks: [
          'Strengthen domestic resource mobilization',
          'Implement all development assistance commitments',
          'Mobilize financial resources for developing countries'
        ]
      }
    };
    
    return briefInfo[code] || {
      title: 'Unknown SDG',
      description: 'Information not available',
      keyTasks: []
    };
  };
  
  return (
    <div className="space-y-6">
      {/* Top Result Highlight */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-green-100 rounded-lg">
            <Trophy className="w-5 h-5 text-green-600" />
          </div>
          <h3 className="text-lg font-bold text-green-800">Top Match</h3>
        </div>
        <div className="text-center">
          <h4 className="text-xl font-bold text-green-900 mb-2">
            Goal {topResult.code}: {topResult.name}
          </h4>
          {/* Confidence score hidden */}
          {/* <div className="inline-block bg-green-100 px-4 py-2 rounded-full border border-green-200">
            <span className="text-green-800 font-bold">
              {topResult.score.toFixed(1)}% confidence
            </span>
          </div> */}
          <div className="mt-2 text-sm text-green-700">
            Model: {modelUsed}
          </div>
        </div>
      </div>

      {/* Pie Chart - Only show if there are significant results */}
      {significantResults.length > 1 && (
        <div className="bg-white rounded-xl p-6 border border-gray-100">
          <div className="flex items-center gap-2 mb-4">
            <BarChart3 className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-bold text-gray-900">
              Distribution Overview
            </h3>
          </div>
          <PieChart results={significantResults} />
        </div>
      )}

      {/* SDG Brief Information Section */}
      {significantResults.length > 0 && (
        <div className="bg-white rounded-xl p-6 border border-gray-100">
          <div className="flex items-center gap-2 mb-6">
            <BookOpen className="w-5 h-5 text-purple-600" />
            <h3 className="text-lg font-bold text-gray-900">
              Brief Information on Identified SDGs
            </h3>
          </div>
          <div className="grid gap-4">
            {significantResults.slice(0, 5).map((result) => {
              const briefInfo = getSDGBriefInfo(result.code);
              const getSDGColor = (code: string) => {
                const colors: { [key: string]: string } = {
                  '1': 'bg-red-600', '2': 'bg-yellow-600', '3': 'bg-green-600', '4': 'bg-red-700',
                  '5': 'bg-red-500', '6': 'bg-blue-400', '7': 'bg-yellow-500', '8': 'bg-red-800',
                  '9': 'bg-orange-600', '10': 'bg-pink-600', '11': 'bg-orange-500', '12': 'bg-yellow-700',
                  '13': 'bg-green-700', '14': 'bg-blue-600', '15': 'bg-green-500', '16': 'bg-blue-800',
                  '17': 'bg-blue-900'
                };
                return colors[code] || 'bg-gray-500';
              };
              // Show keywords as chips if available (simulate for now)
              const keywords = result.keywords || [];
              return (
                <div key={result.code} className="border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors">
                  <div className="flex items-start gap-4">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm ${getSDGColor(result.code)}`}>
                      {result.code}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900">
                          Goal {result.code}: {briefInfo.title}
                        </h4>
                        {/* Hide confidence score */}
                        {/* <span className="text-sm font-medium text-blue-600">
                          {result.score.toFixed(1)}% match
                        </span> */}
                      </div>
                      <p className="text-gray-600 text-sm mb-3">
                        {briefInfo.description}
                      </p>
                      {/* Show keywords as clickable chips if present */}
                      {keywords.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-2">
                          {keywords.map((kw: string, i: number) => (
                            <span
                              key={i}
                              className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs cursor-pointer hover:bg-blue-200"
                              onClick={() => {
                                // Feed keyword to SDGKeywordsSearch search box (simulate via window event)
                                window.dispatchEvent(new CustomEvent('sdg-keyword-search', { detail: kw }));
                              }}
                            >
                              {kw}
                            </span>
                          ))}
                        </div>
                      )}
                      <div>
                        <h5 className="text-xs font-semibold text-gray-700 mb-2 uppercase tracking-wide">
                          Top 3 Key Tasks & Targets:
                        </h5>
                        <ul className="text-xs text-gray-600 space-y-1">
                          {briefInfo.keyTasks.map((task, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-purple-500 mt-1">•</span>
                              <span>{task}</span>
                            </li>
                          ))}
                        </ul>
                        {/* UN SDG Link */}
                        <div className="mt-3 pt-2 border-t border-gray-100">
                          <a
                            href={`https://sdgs.un.org/goals/goal${result.code}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-700 font-medium hover:underline transition-colors"
                          >
                            <ExternalLink className="w-3 h-3" />
                            Learn more about SDG {result.code} on UN website
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          {significantResults.length > 5 && (
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-500">
                Showing brief information for top 5 SDGs. Click on individual results below for more details.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Top Results List */}
      <div className="bg-white rounded-xl p-6 border border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Info className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-bold text-gray-900">
              {showAllResults ? 'All Results' : 'Top Results'}
            </h3>
          </div>
          {results.length > 5 && (
            <button
              onClick={() => setShowAllResults(!showAllResults)}
              className="flex items-center gap-1 text-blue-600 hover:text-blue-700 font-medium text-sm"
            >
              {showAllResults ? (
                <>
                  Show Less <ChevronUp className="w-4 h-4" />
                </>
              ) : (
                <>
                  Show All ({results.length}) <ChevronDown className="w-4 h-4" />
                </>
              )}
            </button>
          )}
        </div>
        <div className="space-y-2">
          {(showAllResults ? results : topResults).map((result, index) => (
            <div
              key={result.code}
              onClick={() => setSelectedSDG(selectedSDG?.code === result.code ? null : result)}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                index === 0
                  ? 'bg-green-50 border-green-200 hover:border-green-300'
                  : result.score > 1
                  ? 'bg-white border-gray-200 hover:border-blue-300'
                  : 'bg-gray-50 border-gray-100 hover:border-gray-200 opacity-75'
              } ${selectedSDG?.code === result.code ? 'ring-2 ring-blue-500' : ''}`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-sm font-medium text-gray-500 w-8">
                    #{index + 1}
                  </span>
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      Goal {result.code}: {result.name}
                    </h4>
                    {/* Show keywords as clickable chips if present */}
                    {Array.isArray(result.keywords) && result.keywords.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {result.keywords.map((kw: string, i: number) => (
                          <span
                            key={i}
                            className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs cursor-pointer hover:bg-blue-200"
                            onClick={e => {
                              e.stopPropagation();
                              window.dispatchEvent(new CustomEvent('sdg-keyword-search', { detail: kw }));
                            }}
                          >
                            {kw}
                          </span>
                        ))}
                      </div>
                    )}
                    {selectedSDG?.code === result.code && (
                      <p className="text-sm text-gray-600 mt-1">
                        {result.description}
                      </p>
                    )}
                  </div>
                </div>
                {/* Hide confidence score */}
                {/*
                <div className="text-right">
                  <div className={`text-lg font-bold ${
                    index === 0
                      ? 'text-green-700'
                      : result.score > 1
                      ? 'text-blue-600'
                      : 'text-gray-500'
                  }`}>
                    {result.score.toFixed(1)}%
                  </div>
                  <div className="w-20 bg-gray-200 rounded-full h-2 mt-1">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        index === 0
                          ? 'bg-green-500'
                          : result.score > 1
                          ? 'bg-blue-500'
                          : 'bg-gray-400'
                      }`}
                      style={{ width: `${Math.min(result.score, 100)}%` }}
                    ></div>
                  </div>
                </div>
                */}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Selected SDG Details */}
      {selectedSDG && (
        <SDGDetailsCard result={selectedSDG} rank={results.findIndex(r => r.code === selectedSDG.code) + 1} />
      )}
    </div>
  );
};

export default ResultsDisplay;