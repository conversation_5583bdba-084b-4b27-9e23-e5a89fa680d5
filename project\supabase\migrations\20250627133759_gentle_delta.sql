/*
  # SDG Keywords Database Schema

  1. New Tables
    - `sdg_keywords`
      - `id` (uuid, primary key)
      - `keyword` (text, the actual keyword)
      - `sdg` (text, SDG number 1-17)
      - `category` (text, keyword category)
      - `relevance` (text, high/medium/low)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `sdg_keywords` table
    - Add policy for public read access
    - Add policy for authenticated users to manage keywords
*/

CREATE TABLE IF NOT EXISTS sdg_keywords (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  keyword text NOT NULL,
  sdg text NOT NULL CHECK (sdg IN ('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17')),
  category text NOT NULL,
  relevance text NOT NULL CHECK (relevance IN ('high', 'medium', 'low')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE sdg_keywords ENABLE ROW LEVEL SECURITY;

-- Allow public read access for the search functionality
CREATE POLICY "Anyone can read keywords"
  ON sdg_keywords
  FOR SELECT
  TO public
  USING (true);

-- Allow authenticated users to manage keywords
CREATE POLICY "Authenticated users can manage keywords"
  ON sdg_keywords
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sdg_keywords_sdg ON sdg_keywords(sdg);
CREATE INDEX IF NOT EXISTS idx_sdg_keywords_category ON sdg_keywords(category);
CREATE INDEX IF NOT EXISTS idx_sdg_keywords_relevance ON sdg_keywords(relevance);
CREATE INDEX IF NOT EXISTS idx_sdg_keywords_keyword ON sdg_keywords USING gin(to_tsvector('english', keyword));

-- Insert initial data
INSERT INTO sdg_keywords (keyword, sdg, category, relevance) VALUES
-- SDG 1: No Poverty
('poverty', '1', 'core', 'high'),
('extreme poverty', '1', 'core', 'high'),
('income inequality', '1', 'economic', 'high'),
('social protection', '1', 'policy', 'high'),
('basic services', '1', 'services', 'high'),
('vulnerable populations', '1', 'social', 'high'),
('economic resources', '1', 'economic', 'high'),
('financial inclusion', '1', 'economic', 'high'),
('microfinance', '1', 'economic', 'medium'),
('cash transfers', '1', 'policy', 'medium'),
('social safety nets', '1', 'policy', 'high'),
('livelihood', '1', 'economic', 'medium'),
('subsistence', '1', 'economic', 'medium'),
('destitution', '1', 'core', 'high'),
('homelessness', '1', 'social', 'high'),
('unemployment', '1', 'economic', 'medium'),
('underemployment', '1', 'economic', 'medium'),
('minimum wage', '1', 'policy', 'medium'),
('welfare', '1', 'policy', 'medium'),
('food insecurity', '1', 'social', 'high'),

-- SDG 2: Zero Hunger
('hunger', '2', 'core', 'high'),
('malnutrition', '2', 'health', 'high'),
('food security', '2', 'core', 'high'),
('sustainable agriculture', '2', 'agriculture', 'high'),
('agricultural productivity', '2', 'agriculture', 'high'),
('crop yields', '2', 'agriculture', 'medium'),
('food systems', '2', 'agriculture', 'high'),
('nutrition', '2', 'health', 'high'),
('stunting', '2', 'health', 'high'),
('wasting', '2', 'health', 'high'),
('undernutrition', '2', 'health', 'high'),
('food production', '2', 'agriculture', 'medium'),
('farming', '2', 'agriculture', 'medium'),
('rural development', '2', 'development', 'medium'),
('smallholder farmers', '2', 'agriculture', 'high'),
('agricultural research', '2', 'research', 'medium'),
('seed varieties', '2', 'agriculture', 'medium'),
('livestock', '2', 'agriculture', 'medium'),
('fisheries', '2', 'agriculture', 'medium'),
('food waste', '2', 'sustainability', 'medium'),

-- SDG 3: Good Health and Well-being
('health', '3', 'core', 'high'),
('well-being', '3', 'core', 'high'),
('healthcare', '3', 'services', 'high'),
('maternal mortality', '3', 'health', 'high'),
('child mortality', '3', 'health', 'high'),
('infectious diseases', '3', 'health', 'high'),
('non-communicable diseases', '3', 'health', 'high'),
('mental health', '3', 'health', 'high'),
('universal health coverage', '3', 'policy', 'high'),
('epidemics', '3', 'health', 'high'),
('vaccines', '3', 'health', 'medium'),
('immunization', '3', 'health', 'medium'),
('medicines', '3', 'health', 'medium'),
('medical research', '3', 'research', 'medium'),
('health systems', '3', 'services', 'high'),
('public health', '3', 'policy', 'high'),
('disease prevention', '3', 'health', 'medium'),
('health promotion', '3', 'health', 'medium'),
('life expectancy', '3', 'health', 'medium'),
('substance abuse', '3', 'health', 'medium');

-- Continue with more SDGs (truncated for brevity, but you can add all 17 SDGs)
-- Add trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_sdg_keywords_updated_at
    BEFORE UPDATE ON sdg_keywords
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();