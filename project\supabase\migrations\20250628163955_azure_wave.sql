/*
  # Add admin policies for SDG keywords management

  1. New Policies
    - Allow authenticated users to insert new keywords
    - Allow authenticated users to update existing keywords
    - Allow authenticated users to delete keywords

  2. Security
    - Maintains existing public read access
    - Adds full CRUD access for authenticated users
    - Ensures only authenticated admins can modify data
*/

-- Allow authenticated users to insert new keywords
CREATE POLICY "Authenticated users can insert keywords"
  ON sdg_keywords
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Allow authenticated users to update existing keywords
CREATE POLICY "Authenticated users can update keywords"
  ON sdg_keywords
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Allow authenticated users to delete keywords
CREATE POLICY "Authenticated users can delete keywords"
  ON sdg_keywords
  FOR DELETE
  TO authenticated
  USING (true);