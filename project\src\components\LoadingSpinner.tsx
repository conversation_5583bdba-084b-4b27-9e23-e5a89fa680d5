import React from 'react';

interface LoadingSpinnerProps {
  model: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ model }) => {
  const loadingText = model === 'aurora-sdg' 
    ? 'Analyzing with high-accuracy model (this may take longer)...'
    : 'Analyzing text for SDG classification...';

  return (
    <div className="text-center py-16">
      <div className="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600 mb-4"></div>
      <p className="text-gray-600 text-lg font-medium">{loadingText}</p>
    </div>
  );
};

export default LoadingSpinner;