import React, { useState, useEffect } from 'react';
import { Search, BookOpen, ChevronDown, ChevronUp, X, AlertCircle, BarChart3, <PERSON><PERSON>dingUp, ArrowLeft, ExternalLink } from 'lucide-react';
import { 
  searchKeywords, 
  getKeywordStatistics, 
  testDatabaseConnection, 
  SDGKeyword, 
  SDG_NAMES, 
  KeywordFrequency,
  getKeywordDetails
} from '../lib/supabase';

interface SDGKeywordsSearchProps {
  onKeywordSelect?: (keyword: SDGKeyword) => void;
}

const SDGKeywordsSearch: React.FC<SDGKeywordsSearchProps> = ({ onKeywordSelect }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSDG, setSelectedSDG] = useState<string>('');
  const [searchResults, setSearchResults] = useState<SDGKeyword[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [keywordCounts, setKeywordCounts] = useState<{ [key: string]: number }>({});
  const [totalKeywords, setTotalKeywords] = useState<number>(0);
  const [topFrequencies, setTopFrequencies] = useState<KeywordFrequency[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [dbConnected, setDbConnected] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showStatistics, setShowStatistics] = useState(false);
  const [exploringKeyword, setExploringKeyword] = useState<string | null>(null);
  const [keywordDetails, setKeywordDetails] = useState<SDGKeyword[]>([]);
  const [showAboutSection, setShowAboutSection] = useState(false);

  const sdgNumbers = Array.from({ length: 17 }, (_, i) => (i + 1).toString());

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Test database connection
      const connected = await testDatabaseConnection();
      setDbConnected(connected);

      if (connected) {
        // Load comprehensive statistics
        const stats = await getKeywordStatistics();
        console.log('Loaded statistics:', stats); // Debug log
        setStatistics(stats);
        setKeywordCounts(stats.keywordsBySDG);
        setTotalKeywords(stats.totalKeywords);
        setTopFrequencies(stats.topFrequencies);
      } else {
        setError('Unable to connect to the database. Please check your connection.');
      }
    } catch (err) {
      console.error('Error loading initial data:', err);
      setError('Failed to load keyword database.');
      setDbConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Search functionality with debouncing
  useEffect(() => {
    const performSearch = async () => {
      if (!dbConnected) return;

      if (searchQuery.trim() || selectedSDG) {
        setIsLoading(true);
        setError(null);
        
        try {
          const results = await searchKeywords(searchQuery, selectedSDG, 'alphabetical');
          console.log(`Search results: ${results.length} keywords found`); // Debug log
          setSearchResults(results);
        } catch (err) {
          console.error('Search error:', err);
          setError('Search failed. Please try again.');
          setSearchResults([]);
        } finally {
          setIsLoading(false);
        }
      } else {
        setSearchResults([]);
      }
    };

    const debounceTimer = setTimeout(performSearch, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery, selectedSDG, dbConnected]);

  const handleClearFilters = () => {
    setSearchQuery('');
    setSelectedSDG('');
    setSearchResults([]);
    setError(null);
  };

  const handleExploreKeyword = async (keyword: string) => {
    setIsLoading(true);
    setExploringKeyword(keyword);
    
    try {
      const details = await getKeywordDetails(keyword);
      setKeywordDetails(details);
    } catch (err) {
      console.error('Error exploring keyword:', err);
      setError('Failed to load keyword details.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToStatistics = () => {
    setExploringKeyword(null);
    setKeywordDetails([]);
  };

  const getSDGColor = (sdg: string) => {
    const colors: { [key: string]: string } = {
      '1': 'bg-red-600', '2': 'bg-yellow-600', '3': 'bg-green-600', '4': 'bg-red-700',
      '5': 'bg-red-500', '6': 'bg-blue-400', '7': 'bg-yellow-500', '8': 'bg-red-800',
      '9': 'bg-orange-600', '10': 'bg-pink-600', '11': 'bg-orange-500', '12': 'bg-yellow-700',
      '13': 'bg-green-700', '14': 'bg-blue-600', '15': 'bg-green-500', '16': 'bg-blue-800',
      '17': 'bg-blue-900'
    };
    return colors[sdg] || 'bg-gray-500';
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-100 rounded-lg">
            <BookOpen className="w-6 h-6 text-purple-600" />
          </div>
          <h2 className="text-2xl font-semibold text-gray-900">SDG Keywords Database</h2>
          {!dbConnected && (
            <div className="flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs">
              <AlertCircle className="w-3 h-3" />
              Offline
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {statistics && (
            <button
              onClick={() => setShowStatistics(!showStatistics)}
              className="flex items-center gap-2 px-3 py-2 text-blue-600 hover:text-blue-700 font-medium text-sm border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors"
            >
              <BarChart3 className="w-4 h-4" />
              Statistics
            </button>
          )}
        </div>
      </div>

      {/* Statistics Panel */}
      {showStatistics && statistics && (
        <div className="mb-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
          <div className="flex items-center gap-2 mb-4">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-blue-900">Database Statistics</h3>
            {exploringKeyword && (
              <button
                onClick={handleBackToStatistics}
                className="ml-auto flex items-center gap-1 text-blue-600 hover:text-blue-700 text-sm"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Overview
              </button>
            )}
          </div>

          {/* Exploring specific keyword details */}
          {exploringKeyword && (
            <div className="mb-6">
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <h4 className="font-semibold text-gray-900 mb-3">
                  Exploring: "{exploringKeyword}" ({keywordDetails.length} occurrences)
                </h4>
                
                {isLoading ? (
                  <div className="text-center py-4">
                    <div className="inline-block animate-spin rounded-full h-6 w-6 border-2 border-blue-200 border-t-blue-600"></div>
                    <p className="text-sm text-gray-600 mt-2">Loading details...</p>
                  </div>
                ) : (
                  <div className="grid gap-2">
                    {keywordDetails.map((detail, index) => (
                      <div
                        key={detail.id}
                        onClick={() => onKeywordSelect?.(detail)}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 cursor-pointer transition-all duration-200"
                      >
                        <div className="flex items-center gap-3">
                          <span className="text-xs text-gray-400 w-6">#{index + 1}</span>
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold ${getSDGColor(detail.sdg)}`}>
                            {detail.sdg}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">"{detail.keyword}"</div>
                            <div className="text-xs text-gray-500">SDG {detail.sdg}: {SDG_NAMES[detail.sdg]}</div>
                          </div>
                        </div>
                        <div className="text-xs text-blue-600">Add to text →</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Top 10 Most Common Phrases/Keywords */}
          {!exploringKeyword && topFrequencies.length > 0 && (
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-gray-900">Top 10 Most Common Keywords/Phrases</h4>
              </div>
              
              <div className="grid md:grid-cols-2 gap-3">
                {topFrequencies.map((freq, index) => (
                  <div 
                    key={freq.keyword} 
                    className={`bg-white rounded-lg p-4 border transition-all duration-200 hover:shadow-md cursor-pointer ${
                      index < 3 
                        ? 'border-blue-300 shadow-sm ring-1 ring-blue-100' 
                        : 'border-gray-200 hover:border-blue-200'
                    }`}
                    onClick={() => handleExploreKeyword(freq.keyword)}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                          index === 0 ? 'bg-yellow-500' : 
                          index === 1 ? 'bg-gray-400' : 
                          index === 2 ? 'bg-orange-600' : 'bg-blue-500'
                        }`}>
                          {index + 1}
                        </div>
                        <span className="font-medium text-gray-900 capitalize">"{freq.keyword}"</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-lg font-bold text-blue-600 hover:text-blue-700 cursor-pointer">
                          {freq.count}
                        </span>
                        <span className="text-xs text-gray-500">times</span>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mb-2">
                      {freq.sdgs.slice(0, 8).map(sdg => (
                        <div key={sdg} className={`w-5 h-5 rounded-full flex items-center justify-center text-white text-xs font-bold ${getSDGColor(sdg)}`}>
                          {sdg}
                        </div>
                      ))}
                      {freq.sdgs.length > 8 && (
                        <div className="w-5 h-5 rounded-full bg-gray-400 flex items-center justify-center text-white text-xs font-bold">
                          +{freq.sdgs.length - 8}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-gray-500">
                        Found in {freq.sdgs.length} SDG{freq.sdgs.length !== 1 ? 's' : ''}
                      </div>
                      <div className="text-xs text-blue-600 opacity-75 hover:opacity-100 transition-opacity">
                        Click to explore →
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="space-y-6">
        {/* Source Information Note - Collapsible */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg">
          <button
            onClick={() => setShowAboutSection(!showAboutSection)}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-blue-100 transition-colors rounded-lg"
          >
            <div className="flex items-center gap-3">
              <div className="p-1 bg-blue-100 rounded-lg">
                <BookOpen className="w-4 h-4 text-blue-600" />
              </div>
              <h4 className="font-semibold text-blue-900">About These Keywords</h4>
            </div>
            {showAboutSection ? (
              <ChevronUp className="w-4 h-4 text-blue-600" />
            ) : (
              <ChevronDown className="w-4 h-4 text-blue-600" />
            )}
          </button>
          
          {showAboutSection && (
            <div className="px-4 pb-4">
              <p className="text-blue-800 text-sm mb-3">
                The keywords are extracted from{' '}
                <a 
                  href="https://elsevier.digitalcommonsdata.com/datasets/y2zyy9vwzy/1" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="font-medium underline hover:text-blue-900 inline-flex items-center gap-1"
                >
                  "Elsevier 2023 Sustainable Development Goals (SDGs) Mapping"
                  <ExternalLink className="w-3 h-3" />
                </a>
              </p>
              <div className="bg-blue-100 rounded-lg p-3 border border-blue-200">
                <p className="text-blue-700 text-sm">
                  <strong>Note:</strong> Keywords are periodically updated by Elsevier. In the original context, combinations of keywords, such as "renewable energy" AND "sustainability", are often used to identify specific SDGs. Therefore, applying only one or two keywords to your text will NOT guarantee an exact match.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Database Status */}
        {!dbConnected && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center gap-2 text-red-700 mb-2">
              <AlertCircle className="w-5 h-5" />
              <span className="font-medium">Database Connection Error</span>
            </div>
            <p className="text-red-600 text-sm">
              Unable to connect to the keywords database. Please check your Supabase configuration and try again.
            </p>
          </div>
        )}

        {/* Search Bar */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search keywords (e.g., 'clean water', 'climate change', 'gender equality')..."
            className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
            disabled={!dbConnected}
          />
        </div>

        {/* Filters Row */}
        <div className="flex flex-wrap items-center gap-4">
          {/* SDG Dropdown Filter */}
          <div className="flex-1 min-w-64">
            <select
              value={selectedSDG}
              onChange={(e) => setSelectedSDG(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white disabled:bg-gray-100 disabled:cursor-not-allowed"
              disabled={!dbConnected}
            >
              <option value="">All SDGs ({totalKeywords} keywords)</option>
              {sdgNumbers.map(num => (
                <option key={num} value={num}>
                  SDG {num}: {SDG_NAMES[num]} ({keywordCounts[num] || 0} keywords)
                </option>
              ))}
            </select>
          </div>

          {/* Clear Filters Button */}
          {(selectedSDG || searchQuery) && (
            <button
              onClick={handleClearFilters}
              className="flex items-center gap-2 px-4 py-3 bg-red-50 text-red-700 border border-red-200 rounded-lg hover:bg-red-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!dbConnected}
            >
              <X className="w-4 h-4" />
              Clear All
            </button>
          )}
        </div>

        {/* Active Filters Display */}
        {(selectedSDG || searchQuery) && (
          <div className="flex flex-wrap gap-2">
            {searchQuery && (
              <div className="flex items-center gap-2 px-3 py-1 bg-purple-50 text-purple-700 border border-purple-200 rounded-full text-sm">
                <Search className="w-3 h-3" />
                Search: "{searchQuery}"
              </div>
            )}
            {selectedSDG && (
              <div className="flex items-center gap-2 px-3 py-1 bg-blue-50 text-blue-700 border border-blue-200 rounded-full text-sm">
                <div className={`w-3 h-3 rounded-full ${getSDGColor(selectedSDG)}`}></div>
                SDG {selectedSDG}: {SDG_NAMES[selectedSDG]}
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="w-5 h-5" />
              <span className="font-medium">Error</span>
            </div>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </div>
        )}

        {/* Loading State */}
        {isLoading && dbConnected && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-purple-200 border-t-purple-600"></div>
            <p className="text-gray-600 mt-2">Searching keywords...</p>
          </div>
        )}

        {/* Results - Shows all results */}
        {!isLoading && searchResults.length > 0 && dbConnected && (
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Search Results ({searchResults.length} keywords found)
              </h3>
              <div className="text-sm text-gray-500">
                Click any keyword to add it to your text
              </div>
            </div>

            <div className="grid gap-3">
              {searchResults.map((keyword, index) => (
                <div
                  key={keyword.id}
                  onClick={() => onKeywordSelect?.(keyword)}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 cursor-pointer transition-all duration-200 group"
                >
                  <div className="flex items-center gap-3">
                    <div className="text-xs text-gray-400 w-8 text-center">
                      #{index + 1}
                    </div>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${getSDGColor(keyword.sdg)}`}>
                      {keyword.sdg}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 group-hover:text-purple-700">
                        {keyword.keyword}
                      </div>
                      <div className="text-sm text-gray-500">
                        SDG {keyword.sdg}: {SDG_NAMES[keyword.sdg]}
                      </div>
                    </div>
                  </div>

                  <div className="text-xs text-gray-400 group-hover:text-purple-600 transition-colors">
                    Add to text →
                  </div>
                </div>
              ))}
            </div>

            {/* Show total count at bottom if many results */}
            {searchResults.length > 20 && (
              <div className="mt-4 text-center py-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  Showing all {searchResults.length} results
                </p>
              </div>
            )}
          </div>
        )}

        {/* No Results */}
        {!isLoading && searchResults.length === 0 && (searchQuery || selectedSDG) && dbConnected && !error && (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-700 mb-2">No Keywords Found</h3>
            <p className="text-gray-500 mb-4">
              Try adjusting your search terms or selecting a different SDG category.
            </p>
            <div className="text-sm text-gray-400">
              {searchQuery && selectedSDG 
                ? `No keywords found for "${searchQuery}" in SDG ${selectedSDG}`
                : searchQuery 
                ? `No keywords found for "${searchQuery}"`
                : `No keywords found in SDG ${selectedSDG}`
              }
            </div>
          </div>
        )}

        {/* Default State - Clean and Simple */}
        {!searchQuery && !selectedSDG && dbConnected && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <BookOpen className="w-8 h-8 text-purple-600" />
            </div>
            <p className="text-gray-500 mb-6">
              Enter a keyword or select an SDG category to search our database
            </p>

            {/* Simple Database Info */}
            {statistics && (
              <div className="bg-gray-50 rounded-lg p-6 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-2">{statistics.totalKeywords}</div>
                  <div className="text-sm text-gray-600 mb-4">Total Keywords Available</div>
                </div>
                
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-center gap-2 text-sm text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Connected to database
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SDGKeywordsSearch;