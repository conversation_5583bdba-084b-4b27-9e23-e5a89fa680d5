/*
  # Add user roles and permissions system

  1. User Metadata Enhancement
    - Add role field to user metadata for admin/super_admin roles
    - Update existing admin user with super_admin role

  2. Security
    - Maintains existing authentication system
    - Uses user_metadata for role-based permissions
    - No changes to RLS policies needed
*/

-- Update the existing admin user to have super_admin role
UPDATE auth.users 
SET raw_user_meta_data = COALESCE(raw_user_meta_data, '{}'::jsonb) || '{"role": "super_admin"}'::jsonb
WHERE email = '<EMAIL>';

-- Create a function to check user roles (for future use)
CREATE OR REPLACE FUNCTION get_user_role(user_id uuid)
RETURNS text AS $$
DECLARE
  user_role text;
BEGIN
  SELECT raw_user_meta_data->>'role' INTO user_role
  FROM auth.users
  WHERE id = user_id;
  
  RETURN COALESCE(user_role, 'admin');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_role(uuid) TO authenticated;