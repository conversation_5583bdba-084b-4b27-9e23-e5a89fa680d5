import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ChevronDown, ChevronUp, Info } from 'lucide-react';
import KeywordHighlighter from './KeywordHighlighter';

interface TextInputProps {
  value: string;
  onChange: (value: string) => void;
  onClassify: () => void;
  onClear: () => void;
  isLoading: boolean;
}

const TextInput: React.FC<TextInputProps> = ({
  value,
  onChange,
  onClassify,
  onClear,
  isLoading
}) => {
  // Count words in the text
  const wordCount = value.trim() ? value.trim().split(/\s+/).length : 0;
  const minWords = 50;
  const isWordCountSufficient = wordCount >= minWords;
  const [showExplanation, setShowExplanation] = React.useState(false);

  return (
    <div className="space-y-5">
      <div className="relative">
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Enter your text here for SDG classification analysis..."
          className="w-full h-64 p-4 border border-gray-300 rounded-lg text-base resize-y transition-colors bg-gray-50 focus:bg-white focus:border-blue-500 focus:outline-none"
          disabled={isLoading}
        />
        
        {/* Word count indicator */}
        <div className="absolute bottom-3 right-3 flex items-center gap-2">
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            wordCount === 0 
              ? 'bg-blue-50 text-blue-600 border border-blue-200'
              : isWordCountSufficient 
              ? 'bg-green-100 text-green-700 border border-green-200' 
              : 'bg-amber-100 text-amber-700 border border-amber-200'
          }`}>
            {wordCount === 0 ? 'Min. 50 words' : `${wordCount} / ${minWords} words`}
          </div>
        </div>
      </div>

      {/* Real-time Keyword Analysis */}
      {value.trim() && (
        <div className="bg-white rounded-xl p-6 border border-gray-200 mt-6">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <h3 className="text-sm font-semibold text-gray-900">Real-time Keyword Analysis</h3>
            <button
              onClick={() => setShowExplanation(!showExplanation)}
              className="ml-auto flex items-center gap-1 text-blue-600 hover:text-blue-700 text-xs font-medium transition-colors"
            >
              <Info className="w-3 h-3" />
              How it works
              {showExplanation ? (
                <ChevronUp className="w-3 h-3" />
              ) : (
                <ChevronDown className="w-3 h-3" />
              )}
            </button>
          </div>
          
          {/* Explanatory Note */}
          {showExplanation && (
            <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs font-bold">i</span>
                </div>
                <div className="text-sm">
                  <p className="text-blue-900 font-medium mb-2">📝 This shows keywords from our database found in your text</p>
                  <p className="text-blue-800 mb-2">
                    <strong>Note:</strong> The number of keywords per SDG doesn't determine the final classification. For example, SDG 4 might have many keywords, but SDG 12 could still be the top match.
                  </p>
                  <p className="text-blue-700 text-xs">
                    💡 The Classifier considers context, meaning, and word relationships—not just keyword counts.
                  </p>
                </div>
              </div>
            </div>
          )}
          
          <KeywordHighlighter text={value} className="max-h-64 overflow-y-auto" />
          
          {/* Call-to-Action for Classification */}
          <div className="mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                <Brain className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="text-green-900 font-medium text-sm">Ready for SDG Classification?</p>
                <p className="text-green-700 text-xs mt-1">
                  Click "Classify Text" below to get the SDG analysis results using SDG Classifier.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Word count warning */}
      {wordCount > 0 && !isWordCountSufficient && (
        <div className="flex items-start gap-3 p-4 bg-amber-50 border border-amber-200 rounded-lg">
          <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-amber-800 font-medium text-sm">
              Input at least {minWords} words to ensure better results
            </p>
            <p className="text-amber-700 text-sm mt-1">
              You need {minWords - wordCount} more words for optimal classification accuracy.
            </p>
          </div>
        </div>
      )}

      <div className="flex gap-4">
        <button
          onClick={onClassify}
          disabled={isLoading || !value.trim() || !isWordCountSufficient}
          className="flex-1 bg-blue-500 text-white px-4 py-3 rounded-md text-base font-semibold disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors flex items-center justify-center gap-2"
        >
          <Brain className="w-5 h-5" />
          {isLoading ? 'Classifying...' : 'Classify Text'}
        </button>

        <button
          onClick={onClear}
          disabled={isLoading}
          className="flex-1 bg-red-500 text-white px-4 py-3 rounded-md text-base font-semibold hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          <Eraser className="w-5 h-5" />
          Clear
        </button>
      </div>
    </div>
  );
};

export default TextInput;