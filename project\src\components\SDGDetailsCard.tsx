import React from 'react';
import { Heart, GraduationCap, Droplets, Sun, TrendingUp, Settings, Scale, Building, Recycle, Globe, Fish, TreePine, Gavel, HandHeart, Apple, Users } from 'lucide-react';
import { SDGResult } from '../types/sdg';

interface SDGDetailsCardProps {
  result: SDGResult;
  rank: number;
}

const SDGDetailsCard: React.FC<SDGDetailsCardProps> = ({ result, rank }) => {
  const getSDGIcon = (code: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      '1': <HandHeart className="w-6 h-6" />,
      '2': <Apple className="w-6 h-6" />,
      '3': <Heart className="w-6 h-6" />,
      '4': <GraduationCap className="w-6 h-6" />,
      '5': <Scale className="w-6 h-6" />,
      '6': <Droplets className="w-6 h-6" />,
      '7': <Sun className="w-6 h-6" />,
      '8': <TrendingUp className="w-6 h-6" />,
      '9': <Settings className="w-6 h-6" />,
      '10': <Scale className="w-6 h-6" />,
      '11': <Building className="w-6 h-6" />,
      '12': <Recycle className="w-6 h-6" />,
      '13': <Globe className="w-6 h-6" />,
      '14': <Fish className="w-6 h-6" />,
      '15': <TreePine className="w-6 h-6" />,
      '16': <Gavel className="w-6 h-6" />,
      '17': <Users className="w-6 h-6" />
    };
    return iconMap[code] || <Heart className="w-6 h-6" />;
  };

  const getSDGColor = (code: string) => {
    const colors: { [key: string]: string } = {
      '1': 'bg-red-600',
      '2': 'bg-yellow-600',
      '3': 'bg-green-600',
      '4': 'bg-red-700',
      '5': 'bg-red-500',
      '6': 'bg-blue-400',
      '7': 'bg-yellow-500',
      '8': 'bg-red-800',
      '9': 'bg-orange-600',
      '10': 'bg-pink-600',
      '11': 'bg-orange-500',
      '12': 'bg-yellow-700',
      '13': 'bg-green-700',
      '14': 'bg-blue-600',
      '15': 'bg-green-500',
      '16': 'bg-blue-800',
      '17': 'bg-blue-900'
    };
    return colors[code] || 'bg-gray-500';
  };

  const getTextRelevance = (code: string) => {
    if (code === '3') {
      return "The text discusses research on mental health treatments (specifically antidepressants), their effects on brain activity, and the methodology of a systematic review in this medical field. This aligns strongly with Goal 3's targets related to mental health promotion, access to quality medicines, and medical research advancement.";
    }
    return "The submitted text shows some relevance to this goal based on the classification model's analysis.";
  };

  const getSDGDetails = (code: string) => {
    const details: { [key: string]: string } = {
      '1': "End poverty in all its forms everywhere. This goal aims to eradicate extreme poverty, implement social protection systems, and ensure equal rights to economic resources.",
      '2': "End hunger, achieve food security and improved nutrition and promote sustainable agriculture. This includes ending malnutrition and doubling agricultural productivity.",
      '3': "Ensure healthy lives and promote well-being for all at all ages. This goal focuses on reducing maternal and child mortality, ending epidemics, and achieving universal health coverage.",
      '4': "Ensure inclusive and equitable quality education and promote lifelong learning opportunities for all. This includes ensuring all children complete primary and secondary education.",
      '5': "Achieve gender equality and empower all women and girls. This goal aims to end discrimination, eliminate violence against women, and ensure equal participation in leadership.",
      '6': "Ensure availability and sustainable management of water and sanitation for all. This includes achieving universal access to safe drinking water and adequate sanitation.",
      '7': "Ensure access to affordable, reliable, sustainable and modern energy for all. This goal focuses on increasing renewable energy and improving energy efficiency.",
      '8': "Promote sustained, inclusive and sustainable economic growth, full and productive employment and decent work for all.",
      '9': "Build resilient infrastructure, promote inclusive and sustainable industrialization and foster innovation.",
      '10': "Reduce inequality within and among countries through policies that empower and promote social, economic and political inclusion.",
      '11': "Make cities and human settlements inclusive, safe, resilient and sustainable through better urban planning and management.",
      '12': "Ensure sustainable consumption and production patterns through efficient use of resources and reduction of waste.",
      '13': "Take urgent action to combat climate change and its impacts through mitigation and adaptation measures.",
      '14': "Conserve and sustainably use the oceans, seas and marine resources for sustainable development.",
      '15': "Protect, restore and promote sustainable use of terrestrial ecosystems, manage forests sustainably, and halt biodiversity loss.",
      '16': "Promote peaceful and inclusive societies, provide access to justice for all and build effective, accountable institutions.",
      '17': "Strengthen the means of implementation and revitalize the global partnership for sustainable development."
    };
    return details[code] || "This Sustainable Development Goal addresses key global challenges with specific targets to be achieved by 2030.";
  };

  const isTopResult = rank === 1;

  return (
    <div className={`bg-white rounded-xl p-6 border-2 transition-all duration-300 ${
      isTopResult ? 'border-green-300 bg-green-50' : 'border-gray-200'
    }`}>
      <div className="flex items-start gap-4 mb-4">
        <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white ${getSDGColor(result.code)}`}>
          {getSDGIcon(result.code)}
        </div>
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-sm font-medium text-gray-500">#{rank}</span>
            <h2 className="text-xl font-bold text-gray-900">
              Goal {result.code}: {result.name}
            </h2>
          </div>
          <p className="text-gray-600 mb-2">{result.description}</p>
          <div className={`inline-block px-3 py-1 rounded-full text-sm font-bold ${
            isTopResult ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
          }`}>
            {result.score.toFixed(2)}% confidence
          </div>
        </div>
      </div>
      
      <div className="space-y-4 pt-4 border-t border-gray-200">
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">About this Goal</h3>
          <p className="text-gray-600 text-sm leading-relaxed">{getSDGDetails(result.code)}</p>
        </div>
        
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">Relevance to Your Text</h3>
          <p className="text-gray-600 text-sm leading-relaxed">{getTextRelevance(result.code)}</p>
        </div>
      </div>
    </div>
  );
};

export default SDGDetailsCard;