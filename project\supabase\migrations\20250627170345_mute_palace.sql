/*
# Create SDG Keywords Database Schema

This SQL script creates the complete database schema for the SDG Keywords application.

## What this creates:
1. **Helper Functions**
   - `update_updated_at_column()` - Automatically updates timestamps
   
2. **Main Table: sdg_keywords**
   - `id` (uuid, primary key)
   - `keyword` (text, the keyword/phrase)
   - `sdg` (text, SDG number 1-17)
   - `created_at` (timestamp)
   - `updated_at` (timestamp)

3. **Indexes for Performance**
   - Full-text search index on keywords
   - Index on SDG for filtering

4. **Security (RLS)**
   - Row Level Security enabled
   - Public read access policy
   - Automatic timestamp updates

5. **Data Validation**
   - SDG values restricted to 1-17
*/

-- Create the update_updated_at_column function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create the sdg_keywords table
CREATE TABLE IF NOT EXISTS public.sdg_keywords (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    keyword text NOT NULL,
    sdg text NOT NULL,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    
    -- Constraint to ensure SDG values are valid (1-17)
    CONSTRAINT sdg_keywords_sdg_check CHECK (
        sdg = ANY (ARRAY['1'::text, '2'::text, '3'::text, '4'::text, '5'::text, '6'::text, '7'::text, '8'::text, '9'::text, '10'::text, '11'::text, '12'::text, '13'::text, '14'::text, '15'::text, '16'::text, '17'::text])
    )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sdg_keywords_keyword ON public.sdg_keywords USING gin (to_tsvector('english'::regconfig, keyword));
CREATE INDEX IF NOT EXISTS idx_sdg_keywords_sdg ON public.sdg_keywords USING btree (sdg);

-- Enable Row Level Security
ALTER TABLE public.sdg_keywords ENABLE ROW LEVEL SECURITY;

-- Create policy to allow public read access
DROP POLICY IF EXISTS "Anyone can read keywords" ON public.sdg_keywords;
CREATE POLICY "Anyone can read keywords"
    ON public.sdg_keywords
    FOR SELECT
    TO public
    USING (true);

-- Create trigger for automatic updated_at timestamp
DROP TRIGGER IF EXISTS update_sdg_keywords_updated_at ON public.sdg_keywords;
CREATE TRIGGER update_sdg_keywords_updated_at
    BEFORE UPDATE ON public.sdg_keywords
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data to test the setup
INSERT INTO public.sdg_keywords (keyword, sdg) VALUES
    ('poverty reduction', '1'),
    ('hunger', '2'),
    ('health', '3'),
    ('education', '4'),
    ('gender equality', '5'),
    ('clean water', '6'),
    ('renewable energy', '7'),
    ('economic growth', '8'),
    ('innovation', '9'),
    ('inequality', '10'),
    ('sustainable cities', '11'),
    ('responsible consumption', '12'),
    ('climate change', '13'),
    ('marine life', '14'),
    ('biodiversity', '15'),
    ('peace', '16'),
    ('partnerships', '17')
ON CONFLICT DO NOTHING;