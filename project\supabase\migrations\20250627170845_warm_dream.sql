/*
  # Delete null keyword rows from database

  1. Changes
    - Set replica identity to enable deletions
    - Delete rows with null, empty, or whitespace-only keywords
    - Add NOT NULL constraint to keyword column
    - Add check constraint to prevent empty keywords

  2. Security
    - Maintains existing RLS policies
    - No changes to table permissions
*/

-- Set replica identity to enable deletions
ALTER TABLE sdg_keywords REPLICA IDENTITY FULL;

-- Delete rows with null, empty, or whitespace-only keywords
DELETE FROM sdg_keywords 
WHERE keyword IS NULL 
   OR keyword = '' 
   OR trim(keyword) = '';

-- Add a NOT NULL constraint to prevent future null keywords
ALTER TABLE sdg_keywords 
ALTER COLUMN keyword SET NOT NULL;

-- Add a check constraint to prevent empty or whitespace-only keywords
ALTER TABLE sdg_keywords 
ADD CONSTRAINT keyword_not_empty 
CHECK (keyword IS NOT NULL AND trim(keyword) != '');