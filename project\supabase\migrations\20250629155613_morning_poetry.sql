/*
  # Copy sdg_keywords structure and data to sdg table

  1. Structure Setup
    - Add missing columns (created_at, updated_at) to sdg table
    - Set proper column defaults and constraints
    - Add same constraints as sdg_keywords table

  2. Indexes and Performance
    - Copy all indexes from sdg_keywords to sdg table
    - Full-text search index on keywords
    - Index on SDG for filtering

  3. Security (RLS)
    - Enable Row Level Security on sdg table
    - Copy all policies from sdg_keywords to sdg table

  4. Data Migration
    - Copy all data from sdg_keywords to sdg table
    - Handle UUID type conversion properly
    - Avoid duplicates and ensure data integrity

  5. Triggers
    - Set up automatic updated_at timestamp updates
*/

-- First, ensure the sdg table has the correct structure
DO $$
BEGIN
  -- Add created_at column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'sdg' AND column_name = 'created_at'
  ) THEN
    ALTER TABLE sdg ADD COLUMN created_at timestamptz DEFAULT now();
  END IF;

  -- Add updated_at column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'sdg' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE sdg ADD COLUMN updated_at timestamptz DEFAULT now();
  END IF;
END $$;

-- Ensure proper column types and constraints
ALTER TABLE sdg ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE sdg ALTER COLUMN keyword SET NOT NULL;
ALTER TABLE sdg ALTER COLUMN sdg SET NOT NULL;

-- Add the same constraints as sdg_keywords
DO $$
BEGIN
  -- Add SDG check constraint if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints tc
    JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
    WHERE tc.table_name = 'sdg' AND tc.constraint_type = 'CHECK' AND tc.constraint_name = 'sdg_sdg_check'
  ) THEN
    ALTER TABLE sdg ADD CONSTRAINT sdg_sdg_check 
    CHECK (sdg = ANY (ARRAY['1'::text, '2'::text, '3'::text, '4'::text, '5'::text, '6'::text, '7'::text, '8'::text, '9'::text, '10'::text, '11'::text, '12'::text, '13'::text, '14'::text, '15'::text, '16'::text, '17'::text]));
  END IF;

  -- Add keyword not empty constraint if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints tc
    JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
    WHERE tc.table_name = 'sdg' AND tc.constraint_type = 'CHECK' AND tc.constraint_name = 'sdg_keyword_not_empty'
  ) THEN
    ALTER TABLE sdg ADD CONSTRAINT sdg_keyword_not_empty 
    CHECK (keyword IS NOT NULL AND TRIM(keyword) != '');
  END IF;
END $$;

-- Copy all indexes from sdg_keywords to sdg table
CREATE INDEX IF NOT EXISTS idx_sdg_keyword ON sdg USING gin (to_tsvector('english'::regconfig, keyword));
CREATE INDEX IF NOT EXISTS idx_sdg_sdg ON sdg USING btree (sdg);

-- Enable Row Level Security on sdg table
ALTER TABLE sdg ENABLE ROW LEVEL SECURITY;

-- Drop existing policies on sdg table to avoid conflicts
DROP POLICY IF EXISTS "Anyone can read keywords" ON sdg;
DROP POLICY IF EXISTS "Authenticated users can insert keywords" ON sdg;
DROP POLICY IF EXISTS "Authenticated users can update keywords" ON sdg;
DROP POLICY IF EXISTS "Authenticated users can delete keywords" ON sdg;

-- Copy all policies from sdg_keywords to sdg table
CREATE POLICY "Anyone can read keywords"
  ON sdg
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Authenticated users can insert keywords"
  ON sdg
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update keywords"
  ON sdg
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Authenticated users can delete keywords"
  ON sdg
  FOR DELETE
  TO authenticated
  USING (true);

-- Ensure the update function exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create the update trigger for sdg table
DROP TRIGGER IF EXISTS update_sdg_updated_at ON sdg;
CREATE TRIGGER update_sdg_updated_at
    BEFORE UPDATE ON sdg
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Copy all data from sdg_keywords to sdg table (avoiding duplicates)
-- Handle UUID type conversion properly by casting text to UUID
INSERT INTO sdg (id, keyword, sdg, created_at, updated_at)
SELECT 
  CASE 
    WHEN sk.id IS NOT NULL THEN sk.id::uuid 
    ELSE gen_random_uuid() 
  END as id,
  sk.keyword,
  sk.sdg,
  COALESCE(sk.created_at, now()) as created_at,
  COALESCE(sk.updated_at, now()) as updated_at
FROM sdg_keywords sk
WHERE sk.keyword IS NOT NULL 
  AND TRIM(sk.keyword) != ''
  AND sk.sdg IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM sdg s 
    WHERE s.keyword = sk.keyword AND s.sdg = sk.sdg
  )
ON CONFLICT (id) DO NOTHING;

-- Update any remaining null timestamps in the sdg table
UPDATE sdg 
SET 
  created_at = COALESCE(created_at, now()),
  updated_at = COALESCE(updated_at, now())
WHERE created_at IS NULL OR updated_at IS NULL;

-- Verify the migration worked by checking row counts
DO $$
DECLARE
  sdg_count INTEGER;
  sdg_keywords_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO sdg_count FROM sdg;
  SELECT COUNT(*) INTO sdg_keywords_count FROM sdg_keywords;
  
  RAISE NOTICE 'Migration completed: sdg table has % rows, sdg_keywords table has % rows', sdg_count, sdg_keywords_count;
END $$;