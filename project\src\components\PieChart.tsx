import React from 'react';
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, <PERSON> } from 'chart.js';
import { <PERSON>hn<PERSON> } from 'react-chartjs-2';
import { SDGResult } from '../types/sdg';

ChartJS.register(ArcElement, Tooltip, Legend);

interface PieChartProps {
  results: SDGResult[];
}

const PieChart: React.FC<PieChartProps> = ({ results }) => {
  // Define SDG colors matching the UN color scheme
  const sdgColors = {
    '1': '#e5243b',
    '2': '#dda63a',
    '3': '#4c9f38',
    '4': '#c5192d',
    '5': '#ff3a21',
    '6': '#26bde2',
    '7': '#fcc30b',
    '8': '#a21942',
    '9': '#fd6925',
    '10': '#dd1367',
    '11': '#fd9d24',
    '12': '#bf8b2e',
    '13': '#3f7e44',
    '14': '#0a97d9',
    '15': '#56c02b',
    '16': '#00689d',
    '17': '#19486a'
  };

  const data = {
    labels: results.map(result => `Goal ${result.code}: ${result.name}`),
    datasets: [
      {
        data: results.map(result => result.score),
        backgroundColor: results.map(result => sdgColors[result.code as keyof typeof sdgColors] || '#94a3b8'),
        borderColor: '#ffffff',
        borderWidth: 2,
        hoverOffset: 10,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 20,
          font: {
            size: 12,
            weight: '500',
          },
          color: '#374151',
          generateLabels: (chart: any) => {
            const original = ChartJS.defaults.plugins.legend.labels.generateLabels;
            const labels = original.call(this, chart);
            
            return labels.map((label: any, index: number) => {
              const result = results[index];
              return {
                ...label,
                text: `Goal ${result.code}: ${result.name} (${result.score.toFixed(1)}%)`,
              };
            });
          },
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const result = results[context.dataIndex];
            return `Goal ${result.code}: ${result.score.toFixed(2)}%`;
          },
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        cornerRadius: 8,
        padding: 12,
      },
    },
    cutout: '60%',
    animation: {
      animateScale: true,
      animateRotate: true,
      duration: 1000,
    },
  };

  return (
    <div className="relative h-96">
      <Doughnut data={data} options={options} />
    </div>
  );
};

export default PieChart;