/*
  # Debug keyword count issue

  1. Check actual counts in both tables
  2. Verify which table the app should be using
  3. Clean up any inconsistencies
  4. Force a fresh count
*/

-- Check if both tables exist and their current counts
DO $$
DECLARE
  sdg_exists BOOLEAN;
  sdg_keywords_exists BOOLEAN;
  sdg_count INTEGER := 0;
  sdg_keywords_count INTEGER := 0;
BEGIN
  -- Check if tables exist
  SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'sdg'
  ) INTO sdg_exists;
  
  SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'sdg_keywords'
  ) INTO sdg_keywords_exists;
  
  RAISE NOTICE '=== TABLE EXISTENCE CHECK ===';
  RAISE NOTICE 'sdg table exists: %', sdg_exists;
  RAISE NOTICE 'sdg_keywords table exists: %', sdg_keywords_exists;
  
  -- Get counts if tables exist
  IF sdg_exists THEN
    SELECT COUNT(*) INTO sdg_count FROM sdg;
    RAISE NOTICE 'sdg table count: %', sdg_count;
  END IF;
  
  IF sdg_keywords_exists THEN
    SELECT COUNT(*) INTO sdg_keywords_count FROM sdg_keywords;
    RAISE NOTICE 'sdg_keywords table count: %', sdg_keywords_count;
  END IF;
  
  -- Show which table has more data
  IF sdg_exists AND sdg_keywords_exists THEN
    IF sdg_count > sdg_keywords_count THEN
      RAISE NOTICE 'RECOMMENDATION: Use sdg table (% keywords)', sdg_count;
    ELSIF sdg_keywords_count > sdg_count THEN
      RAISE NOTICE 'RECOMMENDATION: Use sdg_keywords table (% keywords)', sdg_keywords_count;
    ELSE
      RAISE NOTICE 'Both tables have same count: %', sdg_count;
    END IF;
  END IF;
END $$;

-- Check the structure of both tables
DO $$
DECLARE
  rec RECORD;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== TABLE STRUCTURES ===';
  
  -- Check sdg table structure
  RAISE NOTICE 'SDG table columns:';
  FOR rec IN 
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns 
    WHERE table_name = 'sdg'
    ORDER BY ordinal_position
  LOOP
    RAISE NOTICE '  %: % (nullable: %, default: %)', 
      rec.column_name, rec.data_type, rec.is_nullable, COALESCE(rec.column_default, 'none');
  END LOOP;
  
  RAISE NOTICE '';
  RAISE NOTICE 'SDG_KEYWORDS table columns:';
  FOR rec IN 
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns 
    WHERE table_name = 'sdg_keywords'
    ORDER BY ordinal_position
  LOOP
    RAISE NOTICE '  %: % (nullable: %, default: %)', 
      rec.column_name, rec.data_type, rec.is_nullable, COALESCE(rec.column_default, 'none');
  END LOOP;
END $$;

-- Force a fresh count by clearing any potential caching
-- This creates a temporary function that forces a new count
CREATE OR REPLACE FUNCTION get_fresh_keyword_count()
RETURNS INTEGER AS $$
DECLARE
  fresh_count INTEGER;
  timestamp_now BIGINT;
BEGIN
  timestamp_now := EXTRACT(EPOCH FROM NOW()) * 1000;
  
  -- Try sdg table first (since that's what the app is using now)
  SELECT COUNT(*) INTO fresh_count FROM sdg;
  
  RAISE NOTICE 'Fresh count from sdg table at timestamp %: %', timestamp_now, fresh_count;
  
  RETURN fresh_count;
END;
$$ LANGUAGE plpgsql;

-- Execute the fresh count
SELECT get_fresh_keyword_count() as fresh_keyword_count;

-- Check for any recent deletions or changes
DO $$
DECLARE
  recent_changes INTEGER;
  latest_update TIMESTAMPTZ;
BEGIN
  -- Check if there are any recent updates in sdg table
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'sdg' AND column_name = 'updated_at'
  ) THEN
    SELECT COUNT(*), MAX(updated_at) 
    INTO recent_changes, latest_update
    FROM sdg 
    WHERE updated_at > NOW() - INTERVAL '1 hour';
    
    RAISE NOTICE '';
    RAISE NOTICE '=== RECENT ACTIVITY CHECK ===';
    RAISE NOTICE 'Records updated in last hour: %', recent_changes;
    RAISE NOTICE 'Latest update timestamp: %', latest_update;
  END IF;
END $$;

-- Clean up the temporary function
DROP FUNCTION IF EXISTS get_fresh_keyword_count();