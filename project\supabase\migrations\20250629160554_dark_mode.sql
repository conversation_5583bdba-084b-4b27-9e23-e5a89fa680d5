/*
  # Check keyword counts in sdg_keywords table

  1. Analysis
    - Get total count of keywords in sdg_keywords table
    - Get breakdown by SDG
    - Check for data quality issues
    - Compare with what the application should be showing

  2. Purpose
    - Understand the actual data in sdg_keywords table
    - Verify if the application should be using this table instead of sdg table
*/

-- Check total count and basic info for sdg_keywords table
DO $$
DECLARE
  total_count INTEGER;
  unique_count INTEGER;
  sdg_count INTEGER;
BEGIN
  -- Get total count
  SELECT COUNT(*) INTO total_count FROM sdg_keywords;
  
  -- Get unique keyword-sdg combinations
  SELECT COUNT(DISTINCT (keyword, sdg)) INTO unique_count FROM sdg_keywords;
  
  -- Get count of distinct SDGs
  SELECT COUNT(DISTINCT sdg) INTO sdg_count FROM sdg_keywords;
  
  RAISE NOTICE '=== SDG_KEYWORDS TABLE ANALYSIS ===';
  RAISE NOTICE 'Total keywords: %', total_count;
  RAISE NOTICE 'Unique keyword-sdg combinations: %', unique_count;
  RAISE NOTICE 'Number of SDGs represented: %', sdg_count;
  RAISE NOTICE 'Duplicates: %', total_count - unique_count;
END $$;

-- Show breakdown by SDG for sdg_keywords table
DO $$
DECLARE
  rec RECORD;
  total_keywords INTEGER := 0;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== SDG_KEYWORDS BREAKDOWN BY SDG ===';
  RAISE NOTICE 'SDG | Count | Percentage';
  RAISE NOTICE '----+-------+-----------';
  
  FOR rec IN 
    SELECT 
      sdg,
      COUNT(*) as count,
      ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sdg_keywords)), 2) as percentage
    FROM sdg_keywords 
    GROUP BY sdg 
    ORDER BY sdg::integer
  LOOP
    total_keywords := total_keywords + rec.count;
    RAISE NOTICE '% | % | %%%', 
      LPAD(rec.sdg, 3), 
      LPAD(rec.count::text, 5), 
      LPAD(rec.percentage::text, 8);
  END LOOP;
  
  RAISE NOTICE '----+-------+-----------';
  RAISE NOTICE 'Total: %', total_keywords;
END $$;

-- Check for data quality issues in sdg_keywords
DO $$
DECLARE
  null_keywords INTEGER;
  null_sdgs INTEGER;
  empty_keywords INTEGER;
  invalid_sdgs INTEGER;
BEGIN
  -- Check for null keywords
  SELECT COUNT(*) INTO null_keywords FROM sdg_keywords WHERE keyword IS NULL;
  
  -- Check for null SDGs
  SELECT COUNT(*) INTO null_sdgs FROM sdg_keywords WHERE sdg IS NULL;
  
  -- Check for empty/whitespace keywords
  SELECT COUNT(*) INTO empty_keywords FROM sdg_keywords WHERE TRIM(keyword) = '';
  
  -- Check for invalid SDG values (not 1-17)
  SELECT COUNT(*) INTO invalid_sdgs FROM sdg_keywords 
  WHERE sdg NOT IN ('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17');
  
  RAISE NOTICE '';
  RAISE NOTICE '=== DATA QUALITY CHECK FOR SDG_KEYWORDS ===';
  RAISE NOTICE 'NULL keywords: %', null_keywords;
  RAISE NOTICE 'NULL SDGs: %', null_sdgs;
  RAISE NOTICE 'Empty keywords: %', empty_keywords;
  RAISE NOTICE 'Invalid SDG values: %', invalid_sdgs;
END $$;

-- Show some sample keywords from sdg_keywords
DO $$
DECLARE
  rec RECORD;
  counter INTEGER := 0;
BEGIN
  RAISE NOTICE '';
  RAISE NOTICE '=== SAMPLE KEYWORDS FROM SDG_KEYWORDS TABLE ===';
  RAISE NOTICE 'First 10 keywords (alphabetically):';
  
  FOR rec IN 
    SELECT keyword, sdg 
    FROM sdg_keywords 
    WHERE keyword IS NOT NULL AND TRIM(keyword) != ''
    ORDER BY keyword 
    LIMIT 10 
  LOOP
    counter := counter + 1;
    RAISE NOTICE '  %: "%" (SDG %)', counter, rec.keyword, rec.sdg;
  END LOOP;
END $$;

-- Check if there are any recent timestamps
DO $$
DECLARE
  has_created_at BOOLEAN;
  has_updated_at BOOLEAN;
  latest_created DATE;
  latest_updated DATE;
BEGIN
  -- Check if timestamp columns exist
  SELECT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'sdg_keywords' AND column_name = 'created_at'
  ) INTO has_created_at;
  
  SELECT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'sdg_keywords' AND column_name = 'updated_at'
  ) INTO has_updated_at;
  
  RAISE NOTICE '';
  RAISE NOTICE '=== TIMESTAMP INFORMATION ===';
  RAISE NOTICE 'Has created_at column: %', has_created_at;
  RAISE NOTICE 'Has updated_at column: %', has_updated_at;
  
  IF has_created_at THEN
    SELECT MAX(created_at::date) INTO latest_created FROM sdg_keywords WHERE created_at IS NOT NULL;
    RAISE NOTICE 'Latest created_at date: %', latest_created;
  END IF;
  
  IF has_updated_at THEN
    SELECT MAX(updated_at::date) INTO latest_updated FROM sdg_keywords WHERE updated_at IS NOT NULL;
    RAISE NOTICE 'Latest updated_at date: %', latest_updated;
  END IF;
END $$;