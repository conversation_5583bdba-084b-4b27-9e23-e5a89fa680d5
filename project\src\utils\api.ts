import { SDGResult, APIResponse } from '../types/sdg';

const SDG_DESCRIPTIONS: { [key: string]: string } = {
  '1': 'End poverty in all its forms everywhere',
  '2': 'End hunger, achieve food security and improved nutrition',
  '3': 'Ensure healthy lives and promote well-being for all at all ages',
  '4': 'Ensure inclusive and equitable quality education',
  '5': 'Achieve gender equality and empower all women and girls',
  '6': 'Ensure availability and sustainable management of water',
  '7': 'Ensure access to affordable, reliable, sustainable energy',
  '8': 'Promote sustained, inclusive and sustainable economic growth',
  '9': 'Build resilient infrastructure and foster innovation',
  '10': 'Reduce inequality within and among countries',
  '11': 'Make cities inclusive, safe, resilient and sustainable',
  '12': 'Ensure sustainable consumption and production patterns',
  '13': 'Take urgent action to combat climate change',
  '14': 'Conserve and sustainably use the oceans and marine resources',
  '15': 'Protect, restore and promote sustainable use of ecosystems',
  '16': 'Promote peaceful and inclusive societies for sustainable development',
  '17': 'Strengthen the means of implementation and revitalize partnerships'
};

export const classifyText = async (text: string, model: string): Promise<SDGResult[]> => {
  const apiUrl = `https://aurora-sdg.labs.vu.nl/classifier/classify/${model}`;
  
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text }),
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error(`The ${model} model endpoint is not available. Please try a different model.`);
      } else if (response.status === 401 || response.status === 403) {
        throw new Error(`Authentication failed for ${model} model. The API may require an access key.`);
      } else if (response.status >= 500) {
        throw new Error(`Server error (${response.status}). The ${model} service may be temporarily unavailable.`);
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    }

    const data: APIResponse = await response.json();
    
    // Check if we have valid predictions
    if (!data.predictions || data.predictions.length === 0) {
      return [];
    }

    const results = data.predictions
      .map(prediction => ({
        code: prediction.sdg.code,
        name: prediction.sdg.name,
        description: SDG_DESCRIPTIONS[prediction.sdg.code] || 'Description not available',
        score: prediction.prediction * 100
      }))
      .filter(result => result.score >= 0.1) // Filter out very low scores
      .sort((a, b) => b.score - a.score);
    
    return results;
      
  } catch (error) {
    console.error('API Error:', error);
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('Network error: Unable to connect to the classification service. Please check your internet connection and try again.');
    }
    
    if (error instanceof Error) {
      throw error; // Re-throw our custom errors
    }
    
    throw new Error('An unexpected error occurred during classification. Please try again.');
  }
};