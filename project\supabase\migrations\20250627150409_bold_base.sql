-- Create a function to efficiently count keywords by SDG
CREATE OR REPLACE FUNCTION get_keyword_counts_by_sdg()
RETURNS TABLE(sdg text, count bigint) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    k.sdg,
    COUNT(*) as count
  FROM sdg_keywords k
  GROUP BY k.sdg
  ORDER BY k.sdg;
END;
$$ LANGUAGE plpgsql;

-- <PERSON> execute permission to public (for read access)
GRANT EXECUTE ON FUNCTION get_keyword_counts_by_sdg() TO public;