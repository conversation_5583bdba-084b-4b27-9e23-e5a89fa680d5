/*
  # Clean up null values in sdg_keywords table

  1. Data Cleanup
    - Remove rows where `id` is null (primary identifier should not be null)
    - Remove rows where `sdg` is null (SDG classification is essential)
    - Remove rows where `keyword` is null or empty (content is required)

  2. Data Integrity
    - Ensures all remaining rows have valid, non-null essential data
    - Improves query performance by removing incomplete records
    - Maintains data quality standards

  Note: This operation will permanently delete rows with null values.
  Make sure to backup data if needed before running this migration.
*/

-- Delete rows where id is null
DELETE FROM sdg_keywords 
WHERE id IS NULL;

-- Delete rows where sdg is null
DELETE FROM sdg_keywords 
WHERE sdg IS NULL;

-- Delete rows where keyword is null or empty/whitespace only
DELETE FROM sdg_keywords 
WHERE keyword IS NULL 
   OR TRIM(keyword) = '';

-- Optional: Add a comment about the cleanup
-- This helps track what was cleaned up
INSERT INTO sdg_keywords (id, keyword, sdg) 
SELECT 
  'cleanup_log_' || extract(epoch from now())::text,
  'Data cleanup completed - removed null values on ' || now()::date,
  '17'
WHERE NOT EXISTS (
  SELECT 1 FROM sdg_keywords 
  WHERE keyword LIKE 'Data cleanup completed%'
);