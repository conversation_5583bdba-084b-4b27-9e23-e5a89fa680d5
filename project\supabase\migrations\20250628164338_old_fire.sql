/*
  # Add updated_at column to sdg_keywords table

  1. Changes
    - Add `updated_at` column to `sdg_keywords` table with default value of current timestamp
    - Add `created_at` column to `sdg_keywords` table for completeness
    - Set up trigger to automatically update `updated_at` on row changes
    - Backfill existing records with current timestamp

  2. Security
    - No changes to existing RLS policies
    - Maintains all existing permissions
*/

-- Add created_at and updated_at columns to sdg_keywords table
DO $$
BEGIN
  -- Add created_at column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'sdg_keywords' AND column_name = 'created_at'
  ) THEN
    ALTER TABLE sdg_keywords ADD COLUMN created_at timestamptz DEFAULT now();
  END IF;

  -- Add updated_at column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'sdg_keywords' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE sdg_keywords ADD COLUMN updated_at timestamptz DEFAULT now();
  END IF;
END $$;

-- Update existing records to have current timestamp for both columns
UPDATE sdg_keywords 
SET 
  created_at = COALESCE(created_at, now()),
  updated_at = COALESCE(updated_at, now())
WHERE created_at IS NULL OR updated_at IS NULL;

-- Ensure the trigger function exists (it should already exist based on schema)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger if it doesn't already exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_sdg_keywords_updated_at'
  ) THEN
    CREATE TRIGGER update_sdg_keywords_updated_at
      BEFORE UPDATE ON sdg_keywords
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;