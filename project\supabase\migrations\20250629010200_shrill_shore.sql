/*
  # Fix ID constraint and ensure proper UUID generation

  1. Changes
    - Ensure id column has proper default UUID generation
    - Fix any constraint issues
    - Ensure proper column setup

  2. Security
    - Maintains existing RLS policies
    - No changes to permissions
*/

-- Ensure the id column has proper UUID default
ALTER TABLE sdg_keywords 
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- Make sure the id column is properly set as primary key
DO $$
BEGIN
  -- Check if primary key constraint exists, if not add it
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'sdg_keywords' 
    AND constraint_type = 'PRIMARY KEY'
  ) THEN
    ALTER TABLE sdg_keywords ADD PRIMARY KEY (id);
  END IF;
END $$;

-- Ensure the sdg column type is consistent
ALTER TABLE sdg_keywords 
ALTER COLUMN sdg TYPE text;

-- Update any existing null IDs with new UUIDs
UPDATE sdg_keywords 
SET id = gen_random_uuid() 
WHERE id IS NULL;