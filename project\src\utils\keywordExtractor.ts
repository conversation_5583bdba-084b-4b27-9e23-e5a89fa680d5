/**
 * FlashText-inspired keyword extraction algorithm
 * Based on Aho-Corasick algorithm for efficient keyword matching
 */

export interface KeywordMatch {
  keyword: string;
  sdg: string;
  startIndex: number;
  endIndex: number;
  confidence: number;
}

export interface KeywordNode {
  children: Map<string, KeywordNode>;
  isEndOfKeyword: boolean;
  keywords: Array<{ keyword: string; sdg: string }>;
}

export class KeywordExtractor {
  private root: KeywordNode;
  private keywordCount: number = 0;

  constructor() {
    this.root = {
      children: new Map(),
      isEndOfKeyword: false,
      keywords: []
    };
  }

  /**
   * Add a keyword to the trie structure
   */
  addKeyword(keyword: string, sdg: string): void {
    const cleanKeyword = keyword.toLowerCase().trim();
    if (!cleanKeyword || cleanKeyword.length < 2) return;

    let currentNode = this.root;
    
    for (const char of cleanKeyword) {
      if (!currentNode.children.has(char)) {
        currentNode.children.set(char, {
          children: new Map(),
          isEndOfKeyword: false,
          keywords: []
        });
      }
      currentNode = currentNode.children.get(char)!;
    }
    
    currentNode.isEndOfKeyword = true;
    
    // Avoid duplicates
    const exists = currentNode.keywords.some(k => 
      k.keyword === cleanKeyword && k.sdg === sdg
    );
    
    if (!exists) {
      currentNode.keywords.push({ keyword: cleanKeyword, sdg });
      this.keywordCount++;
    }
  }

  /**
   * Build the keyword extractor from a list of keywords
   */
  buildFromKeywords(keywords: Array<{ keyword: string; sdg: string }>): void {
    console.log(`🔧 Building keyword extractor with ${keywords.length} keywords...`);
    
    // Clear existing data
    this.root = {
      children: new Map(),
      isEndOfKeyword: false,
      keywords: []
    };
    this.keywordCount = 0;

    // Add all keywords to the trie
    keywords.forEach(({ keyword, sdg }) => {
      this.addKeyword(keyword, sdg);
    });

    console.log(`✅ Keyword extractor built with ${this.keywordCount} processed keywords`);
  }

  /**
   * Extract keywords from text using FlashText-inspired algorithm
   */
  extractKeywords(text: string): KeywordMatch[] {
    if (!text || text.length === 0) return [];

    const matches: KeywordMatch[] = [];
    const textLower = text.toLowerCase();
    const textLength = text.length;

    for (let i = 0; i < textLength; i++) {
      let currentNode = this.root;
      let j = i;
      let longestMatch: { keywords: Array<{ keyword: string; sdg: string }>, endIndex: number } | null = null;

      // Try to match keywords starting at position i
      while (j < textLength && currentNode.children.has(textLower[j])) {
        currentNode = currentNode.children.get(textLower[j])!;
        j++;

        // If we found a complete keyword, record it (but keep looking for longer matches)
        if (currentNode.isEndOfKeyword && currentNode.keywords.length > 0) {
          // Enhanced word boundary checking
          const beforeChar = i > 0 ? textLower[i - 1] : ' ';
          const afterChar = j < textLength ? textLower[j] : ' ';
          
          const matchText = text.substring(i, j);
          const isValidMatch = this.isValidKeywordMatch(matchText, beforeChar, afterChar, textLower, i, j);

          if (isValidMatch) {
            longestMatch = {
              keywords: [...currentNode.keywords],
              endIndex: j
            };
          }
        }
      }

      // If we found a match, add it to results
      if (longestMatch) {
        const matchText = text.substring(i, longestMatch.endIndex);
        
        // Sort keywords by SDG number to ensure consistent ordering
        const sortedKeywords = longestMatch.keywords.sort((a, b) => parseInt(a.sdg) - parseInt(b.sdg));
        
        // Add all SDGs for this keyword (some keywords appear in multiple SDGs)
        sortedKeywords.forEach(({ keyword, sdg }) => {
          const confidence = this.calculateConfidence(keyword, matchText, text, i);
          
          matches.push({
            keyword: matchText, // Use original case from text
            sdg,
            startIndex: i,
            endIndex: longestMatch!.endIndex,
            confidence
          });
        });

        // Skip ahead to avoid overlapping matches
        i = longestMatch.endIndex - 1;
      }
    }

    // Sort by position first, then by SDG number, then by confidence
    return matches
      .filter(match => match.confidence > 0.3)
      .sort((a, b) => {
        // First sort by position in text
        if (a.startIndex !== b.startIndex) {
          return a.startIndex - b.startIndex;
        }
        // Then by SDG number for consistent ordering
        if (a.sdg !== b.sdg) {
          return parseInt(a.sdg) - parseInt(b.sdg);
        }
        // Finally by confidence (higher first)
        return b.confidence - a.confidence;
      });
  }

  /**
   * Enhanced validation for keyword matches
   */
  private isValidKeywordMatch(
    matchText: string, 
    beforeChar: string, 
    afterChar: string, 
    fullTextLower: string, 
    startIndex: number, 
    endIndex: number
  ): boolean {
    // Basic word boundary check
    const isWordBoundary = this.isWordBoundary(beforeChar) && this.isWordBoundary(afterChar);
    
    // For phrases like "crystalline shell approximately", we want to extract "crystalline shell"
    // Check if the match ends with an adverb or modifier that should be excluded
    const words = matchText.toLowerCase().split(/\s+/);
    const lastWord = words[words.length - 1];
    
    // If the last word is an adverb/modifier, try to find a better boundary
    if (this.isAdverbOrModifier(lastWord) && words.length > 1) {
      // Find the position where the meaningful part ends
      let meaningfulEndIndex = startIndex;
      let wordStartIndex = startIndex;
      
      for (let i = 0; i < words.length - 1; i++) {
        const wordLength = words[i].length;
        meaningfulEndIndex = wordStartIndex + wordLength;
        
        // Skip spaces to find next word
        while (meaningfulEndIndex < endIndex && /\s/.test(fullTextLower[meaningfulEndIndex])) {
          meaningfulEndIndex++;
        }
        wordStartIndex = meaningfulEndIndex;
      }
      
      // Check if this truncated version has better word boundaries
      const truncatedAfterChar = meaningfulEndIndex < fullTextLower.length ? fullTextLower[meaningfulEndIndex] : ' ';
      if (this.isWordBoundary(truncatedAfterChar)) {
        return true;
      }
    }
    
    // Standard validation
    if (isWordBoundary) return true;
    
    // Allow partial matches for technical terms and longer phrases
    return this.isPartialWordMatch(fullTextLower, startIndex, endIndex);
  }
  /**
   * Check if character is a word boundary
   */
  private isWordBoundary(char: string): boolean {
    return /[\s\.,;:!?\-\(\)\[\]{}"\'\n\r\t]/.test(char);
  }

  /**
   * Check if this is a valid partial word match (for compound words, technical terms)
   */
  private isPartialWordMatch(text: string, start: number, end: number): boolean {
    const keyword = text.substring(start, end);
    
    // Allow matches for longer technical terms
    if (keyword.length >= 6) return true;
    
    // Allow matches that are part of compound words (like "machine learning")
    if (keyword.includes(' ')) return true;
    
    // Allow matches for specific technical patterns
    const technicalPatterns = [
      /\b\w+ing\b/, // words ending in -ing
      /\b\w+tion\b/, // words ending in -tion
      /\b\w+ment\b/, // words ending in -ment
      /\b\w+ness\b/, // words ending in -ness
    ];
    
    return technicalPatterns.some(pattern => pattern.test(keyword));
  }

  /**
   * Calculate confidence score for a match
   */
  private calculateConfidence(keyword: string, matchText: string, fullText: string, position: number): number {
    let confidence = 0.5; // Base confidence
    
    // Exact match bonus
    if (keyword.toLowerCase() === matchText.toLowerCase()) {
      confidence += 0.3;
    }
    
    // Length bonus (longer keywords are more specific)
    if (keyword.length > 10) confidence += 0.2;
    else if (keyword.length > 6) confidence += 0.1;
    
    // Multi-word bonus (phrases are more specific)
    if (keyword.includes(' ')) confidence += 0.2;
    
    // Technical term bonus
    const technicalTerms = [
      'machine learning', 'neural network', 'artificial intelligence',
      'sustainable development', 'climate change', 'renewable energy',
      'data analysis', 'deep learning', 'natural language processing'
    ];
    
    if (technicalTerms.some(term => keyword.includes(term))) {
      confidence += 0.3;
    }
    
    // Context bonus (surrounded by relevant words)
    const contextWindow = 50;
    const contextStart = Math.max(0, position - contextWindow);
    const contextEnd = Math.min(fullText.length, position + matchText.length + contextWindow);
    const context = fullText.substring(contextStart, contextEnd).toLowerCase();
    
    const relevantContextWords = [
      'research', 'study', 'analysis', 'development', 'implementation',
      'framework', 'approach', 'method', 'technique', 'algorithm',
      'sustainable', 'environmental', 'social', 'economic', 'innovation'
    ];
    
    const contextMatches = relevantContextWords.filter(word => context.includes(word)).length;
    confidence += Math.min(0.2, contextMatches * 0.05);
    
    // Penalty for very common words
    const commonWords = ['and', 'or', 'the', 'of', 'in', 'to', 'for', 'with', 'by'];
    if (commonWords.includes(keyword.toLowerCase())) {
      confidence -= 0.4;
    }
    
    return Math.min(1.0, Math.max(0.0, confidence));
  }

  /**
   * Get statistics about the loaded keywords
   */
  getStatistics(): { totalKeywords: number; avgKeywordLength: number } {
    return {
      totalKeywords: this.keywordCount,
      avgKeywordLength: 0 // Could be calculated if needed
    };
  }

  /**
   * Extract unique keywords/phrases from text using NLP-like techniques
   */
  extractUniqueKeywords(text: string): Array<{ keyword: string; frequency: number; importance: number }> {
    if (!text || text.length === 0) return [];

    // Focus on high-quality phrases first, then meaningful single words
    const highQualityPhrases = this.extractHighQualityPhrases(text);
    const meaningfulWords = this.extractMeaningfulWords(text);
    
    const allKeywords: Array<{ keyword: string; frequency: number; importance: number }> = [];
    
    // Add high-quality phrases (prioritized)
    highQualityPhrases.forEach(item => {
      allKeywords.push(item);
    });
    
    // Add meaningful single words (lower priority)
    meaningfulWords.forEach(item => {
      allKeywords.push(item);
    });
    
    // Normalize and deduplicate
    const normalizedKeywords = this.normalizeKeywords(allKeywords);
    
    // Filter for high quality and sort by importance
    return normalizedKeywords
      .filter(item => item.importance > 0.6) // Only show high-quality keywords
      .sort((a, b) => b.importance - a.importance)
      .slice(0, 15); // Limit to top 15 high-quality keywords
  }

  /**
   * Extract high-quality phrases that are semantically meaningful
   */
  private extractHighQualityPhrases(text: string): Array<{ keyword: string; frequency: number; importance: number }> {
    const sentences = text.split(/[.!?]+/);
    const phraseFreq = new Map<string, number>();
    
    sentences.forEach(sentence => {
      const cleanSentence = sentence.toLowerCase().replace(/[^\w\s]/g, ' ').trim();
      if (cleanSentence.length < 10) return; // Skip very short sentences
      
      const words = cleanSentence.split(/\s+/).filter(word => word.length > 0);
      
      // Extract 2-word phrases (most reliable)
      for (let i = 0; i < words.length - 1; i++) {
        const phrase = `${words[i]} ${words[i + 1]}`;
        if (this.isHighQualityPhrase(phrase, 2)) {
          phraseFreq.set(phrase, (phraseFreq.get(phrase) || 0) + 1);
        }
      }
      
      // Extract 3-word phrases (more selective)
      for (let i = 0; i < words.length - 2; i++) {
        const phrase = `${words[i]} ${words[i + 1]} ${words[i + 2]}`;
        if (this.isHighQualityPhrase(phrase, 3)) {
          phraseFreq.set(phrase, (phraseFreq.get(phrase) || 0) + 1);
        }
      }
    });
    
    // Convert to array and calculate importance
    return Array.from(phraseFreq.entries())
      .map(([phrase, freq]) => ({
        keyword: phrase,
        frequency: freq,
        importance: this.calculatePhraseImportance(phrase, freq, phraseFreq.size)
      }))
      .filter(item => item.importance > 0.7); // High threshold for phrases
  }

  /**
   * Extract meaningful single words
   */
  private extractMeaningfulWords(text: string): Array<{ keyword: string; frequency: number; importance: number }> {
    const words = this.tokenizeText(text);
    const wordFreq = this.calculateWordFrequency(words);
    
    return Object.entries(wordFreq)
      .map(([word, freq]) => ({
        keyword: word,
        frequency: freq,
        importance: this.calculateWordImportance(word, freq, words.length)
      }))
      .filter(item => 
        item.importance > 0.8 && // Very high threshold for single words
        item.keyword.length > 4 && // Longer words only
        this.isMeaningfulSingleWord(item.keyword)
      );
  }

  /**
   * Check if a phrase is high quality and worth extracting
   */
  private isHighQualityPhrase(phrase: string, wordCount: number): boolean {
    const words = phrase.split(' ');
    
    // Basic validation
    if (words.length !== wordCount) return false;
    if (phrase.length < 6) return false;
    
    // Filter out phrases with too many stop words or generic terms
    const meaningfulWords = words.filter(word => 
      !this.isStopWord(word) && 
      !this.isAdverbOrModifier(word) && 
      !this.isGenericDescriptor(word) &&
      word.length > 2
    );
    
    // Must have at least half meaningful words
    if (meaningfulWords.length < Math.ceil(words.length / 2)) return false;
    
    // Check for technical/domain relevance
    if (this.isTechnicalPhrase(phrase)) return true;
    
    // Check for SDG-related content
    if (this.isSDGRelevantPhrase(phrase)) return true;
    
    // Check for specific patterns that indicate quality
    return this.hasQualityPatterns(phrase);
  }

  /**
   * Check if a single word is meaningful enough to extract
   */
  private isMeaningfulSingleWord(word: string): boolean {
    // Must not be a generic descriptor or common academic term
    if (this.isGenericDescriptor(word) || this.isAdverbOrModifier(word)) return false;
    
    // Check for technical/scientific terms
    const technicalSuffixes = ['tion', 'ment', 'ness', 'ity', 'ism', 'ogy', 'ics', 'ive', 'ous'];
    const hasTechnicalSuffix = technicalSuffixes.some(suffix => word.endsWith(suffix));
    
    // Check for domain-specific terms
    const domainTerms = [
      'sustainability', 'environmental', 'biodiversity', 'renewable', 'emissions',
      'infrastructure', 'innovation', 'technology', 'governance', 'inequality',
      'urbanization', 'industrialization', 'globalization', 'digitalization',
      'decarbonization', 'electrification', 'automation', 'artificial',
      'intelligence', 'blockchain', 'cryptocurrency', 'nanotechnology',
      'biotechnology', 'pharmaceutical', 'agricultural', 'manufacturing'
    ];
    
    const isDomainTerm = domainTerms.includes(word.toLowerCase());
    
    return hasTechnicalSuffix || isDomainTerm;
  }

  /**
   * Check if phrase is SDG-relevant
   */
  private isSDGRelevantPhrase(phrase: string): boolean {
    const sdgKeywords = [
      'carbon neutrality', 'climate change', 'renewable energy', 'sustainable development',
      'gender equality', 'clean water', 'food security', 'economic growth',
      'social protection', 'environmental protection', 'biodiversity conservation',
      'ocean conservation', 'forest conservation', 'urban planning', 'smart cities',
      'circular economy', 'green technology', 'social inclusion', 'human rights',
      'peace building', 'global partnership', 'capacity building', 'technology transfer',
      'financial inclusion', 'digital divide', 'energy efficiency', 'waste management',
      'water management', 'disaster risk', 'climate adaptation', 'climate mitigation'
    ];
    
    const lowerPhrase = phrase.toLowerCase();
    return sdgKeywords.some(keyword => 
      lowerPhrase.includes(keyword) || keyword.includes(lowerPhrase)
    );
  }

  /**
   * Check for quality patterns in phrases
   */
  private hasQualityPatterns(phrase: string): boolean {
    const words = phrase.split(' ');
    
    // Pattern 1: Adjective + Noun combinations
    const qualityAdjectives = [
      'sustainable', 'renewable', 'clean', 'green', 'smart', 'digital',
      'artificial', 'natural', 'social', 'economic', 'environmental',
      'technological', 'scientific', 'industrial', 'agricultural', 'urban',
      'rural', 'global', 'local', 'national', 'international', 'regional'
    ];
    
    const hasQualityAdjective = words.some(word => 
      qualityAdjectives.includes(word.toLowerCase())
    );
    
    // Pattern 2: Compound technical terms
    const technicalPatterns = [
      /\w+tion/, /\w+ment/, /\w+ness/, /\w+ity/, /\w+ism/,
      /\w+ogy/, /\w+ics/, /\w+ing/, /\w+ed/
    ];
    
    const hasTechnicalPattern = words.some(word =>
      technicalPatterns.some(pattern => pattern.test(word))
    );
    
    // Pattern 3: Specific domain combinations
    const domainCombinations = [
      'investment demand', 'energy infrastructure', 'carbon emissions',
      'economic development', 'social development', 'environmental impact',
      'climate action', 'sustainable growth', 'green economy', 'blue economy',
      'digital transformation', 'energy transition', 'just transition'
    ];
    
    const isDomainCombination = domainCombinations.includes(phrase.toLowerCase());
    
    return hasQualityAdjective || hasTechnicalPattern || isDomainCombination;
  }

  /**
   * Normalize keywords to handle variations like "the paris agreement" vs "paris agreement"
   */
  private normalizeKeywords(keywords: Array<{ keyword: string; frequency: number; importance: number }>): Array<{ keyword: string; frequency: number; importance: number }> {
    const normalizedMap = new Map<string, { keyword: string; frequency: number; importance: number; variations: string[] }>();
    
    keywords.forEach(item => {
      // Normalize the keyword by removing leading articles and common prefixes
      const normalized = this.normalizeKeyword(item.keyword);
      
      if (normalizedMap.has(normalized)) {
        const existing = normalizedMap.get(normalized)!;
        // Combine frequencies and take the higher importance
        existing.frequency += item.frequency;
        existing.importance = Math.max(existing.importance, item.importance);
        existing.variations.push(item.keyword);
        
        // Use the shorter, cleaner version as the display keyword
        if (item.keyword.length < existing.keyword.length && !item.keyword.startsWith('the ')) {
          existing.keyword = item.keyword;
        }
      } else {
        normalizedMap.set(normalized, {
          keyword: item.keyword,
          frequency: item.frequency,
          importance: item.importance,
          variations: [item.keyword]
        });
      }
    });
    
    // Convert back to array format
    return Array.from(normalizedMap.values()).map(item => ({
      keyword: item.keyword,
      frequency: item.frequency,
      importance: item.importance
    }));
  }

  /**
   * Normalize a single keyword by removing articles and standardizing format
   */
  private normalizeKeyword(keyword: string): string {
    let normalized = keyword.toLowerCase().trim();
    
    // Remove leading articles
    const articles = ['the ', 'a ', 'an '];
    for (const article of articles) {
      if (normalized.startsWith(article)) {
        normalized = normalized.substring(article.length);
        break;
      }
    }
    
    // Remove trailing articles and prepositions that don't add meaning
    const trailingWords = [' the', ' a', ' an', ' of', ' in', ' on', ' at', ' to', ' for', ' with', ' by'];
    for (const trailing of trailingWords) {
      if (normalized.endsWith(trailing)) {
        normalized = normalized.substring(0, normalized.length - trailing.length);
        break;
      }
    }
    
    // Handle common variations
    normalized = normalized
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .trim();
    
    return normalized;
  }

  /**
   * Tokenize text into words
   */
  private tokenizeText(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)
      .filter(word => !this.isStopWord(word));
  }

  /**
   * Extract meaningful phrases (2-3 words, focused on quality)
   */
  private extractPhrases(text: string): string[] {
    const sentences = text.split(/[.!?]+/);
    const phrases: string[] = [];
    
    sentences.forEach(sentence => {
      // Tokenize while preserving important prepositions and context
      const rawWords = sentence
        .toLowerCase()
        .replace(/[^\w\s']/g, ' ') // Keep apostrophes for contractions
        .split(/\s+/)
        .filter(word => word.length > 0);
      
      // Create a more sophisticated word filtering that preserves context
      const words = rawWords.filter(word => {
        // Keep words longer than 2 characters
        if (word.length > 2) return true;
        
        // Keep important short prepositions and articles that provide context
        const contextWords = new Set(['in', 'on', 'at', 'of', 'to', 'by', 'for', 'and', 'or']);
        return contextWords.has(word);
      });
      
      // Extract 2-word phrases (filter out adverbs and common modifiers)
      for (let i = 0; i < words.length - 1; i++) {
        const phrase = `${words[i]} ${words[i + 1]}`;
        if (this.isHighQualityPhrase(phrase, 2)) {
          phrases.push(phrase);
        }
      }
      
      // Extract 3-word phrases (more selective filtering)
      for (let i = 0; i < words.length - 2; i++) {
        const phrase = `${words[i]} ${words[i + 1]} ${words[i + 2]}`;
        if (this.isHighQualityPhrase(phrase, 3)) {
          phrases.push(phrase);
        }
      }
    });
    
    return phrases;
  }

  /**
   * Check if a word is an adverb or common modifier that should be filtered out
   */
  private isAdverbOrModifier(word: string): boolean {
    const adverbsAndModifiers = new Set([
      // Common adverbs
      'approximately', 'roughly', 'about', 'around', 'nearly', 'almost', 'quite',
      'very', 'extremely', 'highly', 'significantly', 'substantially', 'considerably',
      'relatively', 'comparatively', 'particularly', 'especially', 'specifically',
      'generally', 'typically', 'usually', 'commonly', 'frequently', 'often',
      'sometimes', 'occasionally', 'rarely', 'seldom', 'never', 'always',
      'completely', 'entirely', 'fully', 'partially', 'partly', 'mostly', 'mainly',
      'primarily', 'largely', 'predominantly', 'essentially', 'basically',
      'fundamentally', 'inherently', 'naturally', 'obviously', 'clearly',
      'apparently', 'seemingly', 'presumably', 'supposedly', 'allegedly',
      'potentially', 'possibly', 'probably', 'likely', 'unlikely', 'certainly',
      'definitely', 'absolutely', 'totally', 'completely', 'perfectly',
      'exactly', 'precisely', 'accurately', 'correctly', 'properly', 'effectively',
      'efficiently', 'successfully', 'directly', 'indirectly', 'immediately',
      'instantly', 'quickly', 'rapidly', 'slowly', 'gradually', 'suddenly',
      'eventually', 'finally', 'ultimately', 'initially', 'originally',
      'previously', 'recently', 'currently', 'presently', 'formerly',
      
      // Quantitative modifiers
      'much', 'many', 'more', 'most', 'less', 'least', 'few', 'several',
      'various', 'numerous', 'multiple', 'single', 'double', 'triple',
      'first', 'second', 'third', 'last', 'final', 'initial', 'primary',
      'secondary', 'tertiary', 'main', 'major', 'minor', 'key', 'central',
      'core', 'basic', 'fundamental', 'essential', 'critical', 'crucial',
      'vital', 'important', 'significant', 'relevant', 'related', 'associated',
      'connected', 'linked', 'combined', 'integrated', 'unified', 'separate',
      'distinct', 'different', 'similar', 'identical', 'same', 'equal',
      'equivalent', 'comparable', 'corresponding', 'respective', 'individual',
      'specific', 'particular', 'certain', 'given', 'selected', 'chosen',
      'preferred', 'desired', 'required', 'necessary', 'needed', 'wanted',
      'expected', 'anticipated', 'predicted', 'estimated', 'calculated',
      'measured', 'observed', 'recorded', 'noted', 'reported', 'stated',
      'mentioned', 'described', 'explained', 'discussed', 'analyzed',
      'examined', 'investigated', 'studied', 'researched', 'tested',
      'evaluated', 'assessed', 'reviewed', 'considered', 'determined',
      'established', 'identified', 'recognized', 'acknowledged', 'confirmed',
      'verified', 'validated', 'proven', 'demonstrated', 'shown', 'revealed',
      'indicated', 'suggested', 'implied', 'proposed', 'recommended',
      'advised', 'suggested', 'urged', 'encouraged', 'supported', 'endorsed',
      
      // Size and degree modifiers
      'thick', 'thin', 'wide', 'narrow', 'broad', 'deep', 'shallow',
      'long', 'short', 'tall', 'low', 'high', 'big', 'small', 'large',
      'huge', 'tiny', 'massive', 'enormous', 'gigantic', 'microscopic',
      'miniature', 'compact', 'dense', 'sparse', 'concentrated', 'diluted',
      'intense', 'mild', 'strong', 'weak', 'powerful', 'gentle', 'harsh',
      'soft', 'hard', 'smooth', 'rough', 'fine', 'coarse', 'sharp', 'dull',
      'bright', 'dark', 'light', 'heavy', 'fast', 'slow', 'quick', 'rapid',
      'gradual', 'sudden', 'immediate', 'delayed', 'early', 'late', 'new',
      'old', 'young', 'ancient', 'modern', 'contemporary', 'current', 'recent',
      'past', 'future', 'present', 'temporary', 'permanent', 'stable', 'unstable',
      'steady', 'variable', 'constant', 'changing', 'fixed', 'flexible',
      'rigid', 'elastic', 'plastic', 'solid', 'liquid', 'gaseous', 'crystalline'
    ]);
    
    return adverbsAndModifiers.has(word.toLowerCase());
  }

  /**
   * Check if a word is a generic descriptor that adds little semantic value
   */
  private isGenericDescriptor(word: string): boolean {
    const genericDescriptors = new Set([
      'case', 'study', 'data', 'assessment', 'evaluation', 'analysis', 'review',
      'report', 'paper', 'article', 'document', 'file', 'record', 'information',
      'details', 'facts', 'evidence', 'proof', 'support', 'example', 'instance',
      'sample', 'specimen', 'model', 'pattern', 'template', 'format', 'structure',
      'form', 'shape', 'design', 'layout', 'arrangement', 'organization',
      'preparation', 'process', 'procedure', 'method', 'technique', 'approach',
      'strategy', 'plan', 'scheme', 'program', 'project', 'initiative', 'effort',
      'attempt', 'trial', 'test', 'experiment', 'investigation', 'research',
      'study', 'survey', 'examination', 'inspection', 'observation', 'monitoring',
      'tracking', 'measurement', 'calculation', 'computation', 'estimation',
      'prediction', 'forecast', 'projection', 'expectation', 'assumption',
      'hypothesis', 'theory', 'concept', 'idea', 'notion', 'thought', 'opinion',
      'view', 'perspective', 'standpoint', 'position', 'stance', 'attitude',
      'approach', 'response', 'reaction', 'feedback', 'input', 'output',
      'outcome', 'result', 'consequence', 'effect', 'impact', 'influence',
      'change', 'modification', 'alteration', 'adjustment', 'adaptation',
      'improvement', 'enhancement', 'development', 'progress', 'advancement',
      'growth', 'expansion', 'extension', 'increase', 'decrease', 'reduction',
      'decline', 'drop', 'fall', 'rise', 'gain', 'loss', 'benefit', 'advantage',
      'disadvantage', 'problem', 'issue', 'challenge', 'difficulty', 'obstacle',
      'barrier', 'limitation', 'constraint', 'restriction', 'requirement',
      'condition', 'situation', 'circumstance', 'context', 'environment',
      'setting', 'background', 'basis', 'foundation', 'ground', 'reason',
      'cause', 'source', 'origin', 'beginning', 'start', 'end', 'finish',
      'conclusion', 'summary', 'overview', 'introduction', 'description',
      'explanation', 'definition', 'meaning', 'significance', 'importance',
      'relevance', 'value', 'worth', 'quality', 'quantity', 'amount', 'number',
      'count', 'total', 'sum', 'average', 'mean', 'median', 'mode', 'range',
      'scope', 'extent', 'degree', 'level', 'grade', 'rank', 'position',
      'status', 'state', 'condition', 'phase', 'stage', 'step', 'part',
      'section', 'portion', 'segment', 'piece', 'unit', 'component', 'element',
      'factor', 'aspect', 'feature', 'characteristic', 'property', 'attribute',
      'trait', 'quality', 'nature', 'type', 'kind', 'sort', 'category',
      'class', 'group', 'set', 'collection', 'series', 'sequence', 'order',
      'arrangement', 'pattern', 'trend', 'tendency', 'direction', 'path',
      'route', 'way', 'means', 'method', 'manner', 'mode', 'style', 'fashion',
      'form', 'format', 'structure', 'framework', 'system', 'network',
      'connection', 'relationship', 'association', 'link', 'bond', 'tie'
    ]);
    
    return genericDescriptors.has(word.toLowerCase());
  }

  /**
   * Check if a phrase is valid and meaningful
   */
  private isValidPhrase(phrase: string): boolean {
    const words = phrase.split(' ');
    
    // Filter out phrases with generic descriptors
    if (words.some(word => this.isGenericDescriptor(word))) {
      return false;
    }
    
    // Filter out phrases that are too generic
    const genericPhrases = new Set([
      'case study', 'data analysis', 'assessment method', 'evaluation process',
      'research study', 'study design', 'data collection', 'data processing',
      'result analysis', 'statistical analysis', 'experimental design',
      'sample preparation', 'method development', 'process optimization',
      'performance evaluation', 'quality assessment', 'risk assessment',
      'impact assessment', 'needs assessment', 'feasibility study',
      'pilot study', 'preliminary study', 'follow up', 'follow up study',
      'literature review', 'systematic review', 'meta analysis',
      'cross sectional', 'longitudinal study', 'cohort study',
      'control group', 'treatment group', 'experimental group',
      'statistical significance', 'significant difference', 'correlation analysis',
      'regression analysis', 'factor analysis', 'cluster analysis',
      'time series', 'trend analysis', 'comparative analysis',
      'cost analysis', 'benefit analysis', 'cost benefit',
      'decision making', 'problem solving', 'information processing',
      'data management', 'project management', 'quality control',
      'quality assurance', 'performance monitoring', 'progress monitoring',
      'outcome measurement', 'impact evaluation', 'effectiveness evaluation',
      'efficiency evaluation', 'process evaluation', 'program evaluation',
      'policy evaluation', 'implementation evaluation', 'monitoring evaluation'
    ]);
    
    if (genericPhrases.has(phrase.toLowerCase())) {
      return false;
    }
    
    // Must contain at least one meaningful word
    const meaningfulWords = words.filter(word => 
      !this.isStopWord(word) && 
      !this.isAdverbOrModifier(word) && 
      !this.isGenericDescriptor(word) &&
      word.length > 3
    );
    
    return meaningfulWords.length >= Math.ceil(words.length / 2);
  }

  /**
   * Check if a phrase is technical/scientific and worth extracting
   */
  private isTechnicalPhrase(phrase: string): boolean {
    const technicalIndicators = [
      // Scientific/technical terms
      'nano', 'micro', 'macro', 'bio', 'eco', 'geo', 'hydro', 'thermo',
      'electro', 'photo', 'radio', 'ultra', 'infra', 'multi', 'inter',
      'trans', 'super', 'hyper', 'meta', 'proto', 'pseudo', 'quasi',
      
      // Technical suffixes
      'tion', 'sion', 'ment', 'ness', 'ity', 'ism', 'ogy', 'ics',
      'ate', 'ize', 'ify', 'ous', 'ive', 'able', 'ible', 'ful',
      
      // Domain-specific terms
      'synthesis', 'catalysis', 'polymer', 'crystal', 'molecule', 'compound',
      'reaction', 'solution', 'mixture', 'phase', 'structure', 'property',
      'material', 'surface', 'interface', 'membrane', 'particle', 'fiber',
      'composite', 'alloy', 'ceramic', 'semiconductor', 'conductor',
      'insulator', 'magnetic', 'electric', 'optical', 'thermal', 'mechanical',
      'chemical', 'biological', 'physical', 'environmental', 'sustainable',
      'renewable', 'biodegradable', 'recyclable', 'efficient', 'effective',
      'innovative', 'advanced', 'novel', 'emerging', 'cutting', 'state',
      'neutrality', 'mitigation', 'adaptation', 'transition', 'transformation',
      'digitalization', 'automation', 'electrification', 'decarbonization',
      'urbanization', 'industrialization', 'globalization', 'localization'
    ];
    
    const lowerPhrase = phrase.toLowerCase();
    return technicalIndicators.some(indicator => lowerPhrase.includes(indicator));
  }

  /**
   * Check if a phrase should be skipped based on its words
   */
  private shouldSkipPhrase(words: string[]): boolean {
    // Skip if all words are stop words, adverbs, or generic descriptors
    const meaningfulWords = words.filter(word => 
      !this.isStopWord(word) && 
      !this.isAdverbOrModifier(word) && 
      !this.isGenericDescriptor(word)
    );
    
    // Skip if less than half the words are meaningful
    return meaningfulWords.length < Math.ceil(words.length / 2);
  }

  /**
   * Check if a long phrase (4+ words) is meaningful enough to extract
   */
  private isMeaningfulLongPhrase(words: string[]): boolean {
    // For 4+ word phrases, we need at least 2 meaningful words
    const meaningfulWords = words.filter(word => 
      !this.isStopWord(word) && 
      !this.isAdverbOrModifier(word) && 
      !this.isGenericDescriptor(word) &&
      word.length > 3
    );
    
    // Must have at least 2 meaningful words for long phrases
    if (meaningfulWords.length < 2) return false;
    
    // Check if it contains technical terms
    const phrase = words.join(' ');
    if (this.isTechnicalPhrase(phrase)) return true;
    
    // Check if it has domain-specific relevance
    const domainRelevantWords = meaningfulWords.filter(word => {
      const domainTerms = [
        'sustainable', 'development', 'environmental', 'social', 'economic',
        'innovation', 'technology', 'research', 'analysis', 'framework',
        'system', 'network', 'process', 'method', 'approach', 'strategy',
        'implementation', 'evaluation', 'assessment', 'monitoring', 'management'
      ];
      return domainTerms.includes(word.toLowerCase());
    });
    
    return domainRelevantWords.length > 0;
  }

  /**
   * Check if a phrase has semantic value beyond generic academic language
   */
  private hasSemanticValue(phrase: string): boolean {
    const words = phrase.toLowerCase().split(' ');
    
    // Check for technical/domain-specific content
    if (this.isTechnicalPhrase(phrase)) return true;
    
    // Check for specific SDG-related terms
    const sdgTerms = [
      'poverty', 'hunger', 'health', 'education', 'gender', 'water', 'energy',
      'economic', 'infrastructure', 'inequality', 'cities', 'consumption',
      'climate', 'ocean', 'biodiversity', 'peace', 'partnership',
      'sustainable', 'development', 'goals', 'environmental', 'social'
    ];
    
    const hasSDGTerm = words.some(word => sdgTerms.includes(word));
    if (hasSDGTerm) return true;
    
    // Check for meaningful combinations
    const meaningfulWords = words.filter(word => 
      !this.isStopWord(word) && 
      !this.isAdverbOrModifier(word) && 
      !this.isGenericDescriptor(word) &&
      word.length > 3
    );
    
    // Must have at least one meaningful word per 2 total words
    return meaningfulWords.length >= Math.ceil(words.length / 2);
  }

  /**
   * Calculate word frequency
   */
  private calculateWordFrequency(words: string[]): { [key: string]: number } {
    const freq: { [key: string]: number } = {};
    words.forEach(word => {
      freq[word] = (freq[word] || 0) + 1;
    });
    return freq;
  }

  /**
   * Calculate phrase frequency
   */
  private calculatePhraseFrequency(phrases: string[]): { [key: string]: number } {
    const freq: { [key: string]: number } = {};
    phrases.forEach(phrase => {
      freq[phrase] = (freq[phrase] || 0) + 1;
    });
    return freq;
  }

  /**
   * Calculate importance score for a word
   */
  private calculateWordImportance(word: string, frequency: number, totalWords: number): number {
    let importance = 0.4; // Base importance for single words
    
    // Frequency bonus (limited)
    importance += Math.min(0.2, frequency * 0.05);
    
    // Length bonus (longer words tend to be more specific)
    if (word.length > 10) importance += 0.4;
    else if (word.length > 8) importance += 0.3;
    else if (word.length > 6) importance += 0.2;
    
    // Technical/academic term bonus
    const technicalSuffixes = ['tion', 'ment', 'ness', 'ity', 'ism', 'ogy', 'ics', 'ive', 'ous'];
    if (technicalSuffixes.some(suffix => word.endsWith(suffix))) {
      importance += 0.3;
    }
    
    // Domain-specific term bonus
    const domainTerms = [
      'sustainability', 'environmental', 'biodiversity', 'renewable', 'emissions',
      'infrastructure', 'innovation', 'technology', 'governance', 'inequality',
      'urbanization', 'industrialization', 'globalization', 'digitalization',
      'decarbonization', 'electrification', 'neutrality', 'mitigation', 'adaptation'
    ];
    
    if (domainTerms.includes(word)) {
      importance += 0.4;
    }
    
    // Penalty for overly frequent words (likely generic)
    if (frequency > totalWords * 0.03) {
      importance *= 0.6;
    }
    
    return Math.min(1.0, importance);
  }

  /**
   * Calculate importance score for a phrase
   */
  private calculatePhraseImportance(phrase: string, frequency: number, totalPhrases: number): number {
    let importance = 0.5; // Base importance
    
    // Frequency bonus (but not too much weight)
    importance += Math.min(0.3, frequency * 0.1);
    
    // Multi-word phrases are generally more specific
    const wordCount = phrase.split(' ').length;
    if (wordCount === 3) importance += 0.4;
    else if (wordCount === 2) importance += 0.3;
    
    // Length bonus
    if (phrase.length > 15) importance += 0.3;
    else if (phrase.length > 10) importance += 0.2;
    
    // SDG relevance bonus
    if (this.isSDGRelevantPhrase(phrase)) {
      importance += 0.4;
    }
    
    // Technical phrase bonus
    if (this.isTechnicalPhrase(phrase)) {
      importance += 0.4;
    }
    
    // Quality pattern bonus
    if (this.hasQualityPatterns(phrase)) {
      importance += 0.3;
    }
    
    // Penalty for generic academic language
    const genericPhrases = [
      'this study', 'this research', 'this paper', 'this article',
      'our study', 'our research', 'our findings', 'our results',
      'the study', 'the research', 'the paper', 'the article',
      'study shows', 'research shows', 'results show', 'findings show',
      'data shows', 'analysis shows', 'evidence shows'
    ];
    
    if (genericPhrases.includes(phrase.toLowerCase())) {
      importance -= 0.5;
    }
    
    return Math.min(1.0, importance);
  }

  /**
   * Check if word is a stop word
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      // Basic articles, prepositions, and conjunctions
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
      'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
      'above', 'below', 'between', 'among', 'under', 'over', 'out', 'off', 'down',
      
      // Verbs and auxiliary verbs
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
      'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
      'must', 'can', 'get', 'got', 'make', 'made', 'take', 'took', 'give', 'gave',
      'come', 'came', 'go', 'went', 'see', 'saw', 'know', 'knew', 'think', 'thought',
      'say', 'said', 'tell', 'told', 'become', 'became', 'find', 'found', 'use', 'used',
      'work', 'worked', 'call', 'called', 'try', 'tried', 'ask', 'asked', 'need', 'needed',
      'feel', 'felt', 'seem', 'seemed', 'leave', 'left', 'put', 'keep', 'kept',
      
      // Pronouns and determiners
      'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
      'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
      'what', 'which', 'who', 'when', 'where', 'why', 'how', 'all', 'any', 'both',
      'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not',
      'only', 'own', 'same', 'so', 'than', 'too', 'very', 'just', 'now', 'here',
      'there', 'then', 'also', 'well', 'back', 'still', 'way', 'even', 'new', 'old',
      
      // Common academic/research words that are not meaningful
      'including', 'resulting', 'based', 'using', 'within', 'across', 'around',
      'against', 'without', 'through', 'during', 'following', 'according', 'regarding',
      'concerning', 'involving', 'containing', 'showing', 'indicating', 'suggesting',
      'demonstrating', 'revealing', 'providing', 'offering', 'presenting', 'describing',
      'explaining', 'discussing', 'examining', 'exploring', 'investigating', 'analyzing',
      'studying', 'reviewing', 'considering', 'evaluating', 'assessing', 'measuring',
      'testing', 'comparing', 'contrasting', 'determining', 'establishing', 'identifying',
      'recognizing', 'understanding', 'learning', 'knowing', 'realizing', 'discovering',
      'finding', 'noting', 'observing', 'seeing', 'looking', 'watching', 'reading',
      'writing', 'saying', 'telling', 'speaking', 'talking', 'asking', 'answering',
      'responding', 'replying', 'reporting', 'stating', 'claiming', 'arguing', 'believing',
      'thinking', 'feeling', 'hoping', 'expecting', 'wanting', 'needing', 'trying',
      'attempting', 'seeking', 'aiming', 'planning', 'preparing', 'beginning', 'starting',
      'continuing', 'proceeding', 'moving', 'going', 'coming', 'arriving', 'reaching',
      'getting', 'becoming', 'remaining', 'staying', 'keeping', 'holding', 'taking',
      'giving', 'bringing', 'sending', 'putting', 'placing', 'setting', 'making',
      'creating', 'building', 'developing', 'producing', 'generating', 'forming',
      'establishing', 'organizing', 'managing', 'controlling', 'operating', 'running',
      'working', 'functioning', 'performing', 'acting', 'behaving', 'conducting',
      'carrying', 'doing', 'happening', 'occurring', 'existing', 'living', 'being',
      
      // Time and quantity words
      'first', 'second', 'third', 'last', 'next', 'previous', 'early', 'late', 'long',
      'short', 'high', 'low', 'big', 'small', 'large', 'little', 'much', 'many',
      'several', 'various', 'different', 'similar', 'same', 'other', 'another',
      'certain', 'particular', 'specific', 'general', 'common', 'usual', 'normal',
      'regular', 'standard', 'typical', 'average', 'main', 'major', 'minor', 'key',
      'important', 'significant', 'relevant', 'related', 'associated', 'connected',
      'linked', 'combined', 'together', 'separate', 'apart', 'alone', 'single',
      'individual', 'personal', 'private', 'public', 'social', 'political', 'economic',
      'financial', 'commercial', 'business', 'professional', 'technical', 'scientific',
      'medical', 'legal', 'educational', 'cultural', 'historical', 'traditional',
      'modern', 'contemporary', 'current', 'recent', 'past', 'future', 'present',
      
      // Additional common academic/research terms to filter out
      'analysis', 'preparation', 'process', 'method', 'approach', 'technique',
      'procedure', 'strategy', 'framework', 'model', 'system', 'structure',
      'concept', 'theory', 'principle', 'factor', 'element', 'component',
      'aspect', 'feature', 'characteristic', 'property', 'attribute', 'quality',
      'condition', 'situation', 'circumstance', 'context', 'environment', 'setting',
      'background', 'basis', 'foundation', 'ground', 'reason', 'cause', 'effect',
      'result', 'outcome', 'consequence', 'impact', 'influence', 'change', 'difference',
      'variation', 'modification', 'adjustment', 'improvement', 'enhancement', 'development',
      'progress', 'advancement', 'growth', 'increase', 'decrease', 'reduction',
      'level', 'degree', 'extent', 'amount', 'number', 'quantity', 'size', 'scale',
      'range', 'scope', 'area', 'field', 'domain', 'sector', 'region', 'zone',
      'part', 'section', 'portion', 'segment', 'piece', 'unit', 'item', 'object',
      'thing', 'matter', 'issue', 'problem', 'question', 'topic', 'subject', 'theme',
      'content', 'material', 'data', 'information', 'knowledge', 'evidence', 'proof',
      'support', 'help', 'assistance', 'aid', 'service', 'resource', 'tool', 'instrument',
      'device', 'equipment', 'facility', 'infrastructure', 'platform', 'interface',
      'connection', 'relationship', 'association', 'correlation', 'interaction', 'communication',
      'collaboration', 'cooperation', 'coordination', 'integration', 'combination', 'mixture',
      'blend', 'merge', 'join', 'unite', 'connect', 'link', 'relate', 'associate',
      'compare', 'contrast', 'distinguish', 'differentiate', 'separate', 'divide', 'split',
      
      // Generic descriptors
      'good', 'bad', 'better', 'best', 'worse', 'worst', 'great', 'excellent',
      'poor', 'fine', 'nice', 'wonderful', 'amazing', 'incredible', 'fantastic',
      'terrible', 'awful', 'horrible', 'beautiful', 'ugly', 'pretty', 'handsome',
      'attractive', 'interesting', 'boring', 'exciting', 'dull', 'fun', 'serious',
      'funny', 'strange', 'weird', 'normal', 'unusual', 'special', 'ordinary',
      'extraordinary', 'remarkable', 'outstanding', 'excellent', 'perfect', 'complete',
      'full', 'empty', 'half', 'whole', 'entire', 'total', 'final', 'ultimate'
    ]);
    
    return stopWords.has(word.toLowerCase());
  }
}

// Singleton instance for the application
export const keywordExtractor = new KeywordExtractor();