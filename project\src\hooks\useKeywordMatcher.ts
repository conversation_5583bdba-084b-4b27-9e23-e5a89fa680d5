import { useState, useEffect } from 'react';
import { SDGKeyword, searchKeywords, testDatabaseConnection } from '../lib/supabase';

interface UseKeywordMatcherOptions {
  enabled?: boolean;
  debounceMs?: number;
}

interface ProcessedKeyword extends SDGKeyword {
  priority: number;
  wordCount: number;
  isPhrase: boolean;
}

export const useKeywordMatcher = (
  text: string, 
  options: UseKeywordMatcherOptions = {}
) => {
  const { enabled = true, debounceMs = 300 } = options;
  
  const [keywords, setKeywords] = useState<ProcessedKeyword[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(true);

  // Load and process keywords when component mounts
  useEffect(() => {
    const loadAllKeywords = async () => {
      if (!enabled) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        // Test database connection first
        const connected = await testDatabaseConnection();
        setIsConnected(connected);
        
        if (!connected) {
          setError('Database connection failed');
          return;
        }

        // Load keywords for each SDG
        const allKeywords: SDGKeyword[] = [];
        const keywordPromises = [];
        
        for (let sdg = 1; sdg <= 17; sdg++) {
          keywordPromises.push(
            searchKeywords('', sdg.toString(), 'alphabetical').catch(err => {
              console.warn(`Failed to load keywords for SDG ${sdg}:`, err);
              return [];
            })
          );
        }
        
        const results = await Promise.all(keywordPromises);
        results.forEach(sdgKeywords => {
          allKeywords.push(...sdgKeywords);
        });
        
        // Process and filter keywords for better matching
        const processedKeywords = allKeywords
          .filter(keyword => keyword.keyword && keyword.keyword.trim().length > 0)
          .map(keyword => {
            const trimmedKeyword = keyword.keyword.trim();
            const wordCount = trimmedKeyword.split(/\s+/).length;
            const isPhrase = wordCount > 1;
            
            // Calculate priority based on keyword characteristics
            let priority = 0;
            
            // Higher priority for multi-word phrases
            if (isPhrase) {
              priority += 10;
            }
            
            // Higher priority for longer keywords (more specific)
            if (trimmedKeyword.length > 10) {
              priority += 5;
            }
            
            // Higher priority for technical/scientific terms
            const technicalTerms = [
              'technology', 'research', 'development', 'management', 'system', 'network',
              'analysis', 'assessment', 'monitoring', 'evaluation', 'implementation',
              'framework', 'methodology', 'approach', 'strategy', 'policy', 'program',
              'initiative', 'project', 'innovation', 'solution', 'application'
            ];
            
            if (technicalTerms.some(term => trimmedKeyword.toLowerCase().includes(term))) {
              priority += 3;
            }
            
            // Lower priority for very common/generic words
            const commonWords = [
              'and', 'or', 'the', 'of', 'in', 'to', 'for', 'with', 'by', 'from',
              'up', 'about', 'into', 'through', 'during', 'before', 'after',
              'above', 'below', 'between', 'among', 'under', 'over', 'at', 'on',
              'off', 'out', 'down', 'up', 'train', 'training', 'work', 'working',
              'use', 'using', 'make', 'making', 'get', 'getting', 'take', 'taking',
              'go', 'going', 'come', 'coming', 'see', 'seeing', 'know', 'knowing'
            ];
            
            if (commonWords.includes(trimmedKeyword.toLowerCase()) || trimmedKeyword.length < 4) {
              priority -= 10;
            }
            
            return {
              ...keyword,
              keyword: trimmedKeyword,
              priority,
              wordCount,
              isPhrase
            } as ProcessedKeyword;
          })
          .filter(keyword => keyword.priority > -5) // Filter out very low priority keywords
          .sort((a, b) => {
            // Sort by priority first, then by length (longer first)
            if (b.priority !== a.priority) {
              return b.priority - a.priority;
            }
            return b.keyword.length - a.keyword.length;
          });
        
        // Remove duplicates
        const uniqueKeywords = processedKeywords.filter((keyword, index, self) => 
          index === self.findIndex(k => k.keyword.toLowerCase() === keyword.keyword.toLowerCase() && k.sdg === keyword.sdg)
        );
        
        setKeywords(uniqueKeywords);
        console.log(`Loaded ${uniqueKeywords.length} processed keywords for smart matching`);
        
      } catch (err) {
        console.error('Error loading keywords for matching:', err);
        setError('Failed to load keywords for matching');
        setIsConnected(false);
      } finally {
        setIsLoading(false);
      }
    };

    loadAllKeywords();
  }, [enabled]);

  // Smart keyword matching based on text content
  const [relevantKeywords, setRelevantKeywords] = useState<SDGKeyword[]>([]);

  useEffect(() => {
    if (!text || !keywords.length) {
      setRelevantKeywords([]);
      return;
    }

    const debounceTimer = setTimeout(() => {
      const textLower = text.toLowerCase();
      const textWords = textLower.split(/\s+/);
      
      // Smart matching algorithm
      const matches: Array<{ keyword: ProcessedKeyword; score: number }> = [];
      
      keywords.forEach(keyword => {
        const keywordLower = keyword.keyword.toLowerCase();
        let score = 0;
        
        // Exact phrase match (highest score)
        if (textLower.includes(keywordLower)) {
          // Check if it's a word boundary match (not part of another word)
          const regex = new RegExp(`\\b${keywordLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
          if (regex.test(text)) {
            score += keyword.isPhrase ? 100 : 50;
          } else if (keyword.keyword.length > 6) {
            // For longer keywords, partial matches are still valuable
            score += 30;
          }
        }
        
        // Multi-word keyword matching
        if (keyword.isPhrase) {
          const keywordWords = keywordLower.split(/\s+/);
          const matchedWords = keywordWords.filter(word => 
            word.length > 3 && textWords.some(textWord => 
              textWord.includes(word) || word.includes(textWord)
            )
          );
          
          if (matchedWords.length === keywordWords.length) {
            score += 80; // All words found
          } else if (matchedWords.length > keywordWords.length / 2) {
            score += 40; // Most words found
          }
        }
        
        // Single word matching with context
        if (!keyword.isPhrase && keyword.keyword.length > 4) {
          const keywordWords = keywordLower.split(/\s+/);
          keywordWords.forEach(word => {
            if (word.length > 3) {
              // Look for exact word matches
              if (textWords.includes(word)) {
                score += 25;
              }
              // Look for word stems/variations
              else if (textWords.some(textWord => 
                (textWord.startsWith(word) && textWord.length <= word.length + 3) ||
                (word.startsWith(textWord) && word.length <= textWord.length + 3)
              )) {
                score += 15;
              }
            }
          });
        }
        
        // Boost score based on keyword priority
        score += keyword.priority;
        
        // Only include keywords with meaningful scores
        if (score > 20) {
          matches.push({ keyword, score });
        }
      });
      
      // Sort by score and take top matches
      const topMatches = matches
        .sort((a, b) => b.score - a.score)
        .slice(0, 50) // Limit to top 50 matches to avoid overwhelming display
        .map(match => match.keyword);
      
      setRelevantKeywords(topMatches);
      console.log(`Found ${topMatches.length} relevant keyword matches`);
      
    }, debounceMs);

    return () => clearTimeout(debounceTimer);
  }, [text, keywords, debounceMs]);

  return {
    keywords: relevantKeywords,
    allKeywords: keywords,
    isLoading,
    error,
    isConnected,
    totalKeywordsLoaded: keywords.length
  };
};