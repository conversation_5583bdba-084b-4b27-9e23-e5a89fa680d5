/*
  # Delete 'or' and 'OR' keywords from database

  1. Changes
    - Remove all rows where keyword is exactly 'or' (case sensitive)
    - Remove all rows where keyword is exactly 'OR' (case sensitive)
    - Clean up any variations with whitespace

  2. Security
    - This operation will permanently delete these keywords
    - No RLS changes needed as this is data cleanup
*/

-- Delete rows where keyword is exactly 'or' (lowercase)
DELETE FROM sdg_keywords 
WHERE keyword = 'or';

-- Delete rows where keyword is exactly 'OR' (uppercase)
DELETE FROM sdg_keywords 
WHERE keyword = 'OR';

-- Delete rows where keyword is 'or' or 'OR' with any surrounding whitespace
DELETE FROM sdg_keywords 
WHERE TRIM(keyword) = 'or' OR TRIM(keyword) = 'OR';

-- Optional: Also remove any other case variations like 'Or', 'oR'
DELETE FROM sdg_keywords 
WHERE LOWER(TRIM(keyword)) = 'or' AND LENGTH(TRIM(keyword)) = 2;