import React, { useState, useEffect } from 'react';
import { LogOut, Plus, Search, Edit, Trash2, Save, X, AlertCircle, CheckCircle, RefreshCw, Database, ChevronLeft, ChevronRight } from 'lucide-react';
import { supabase, SDGKeyword, SDG_NAMES, refreshStatistics } from '../lib/supabase';

interface AdminPanelProps {
  onLogout: () => void;
}

const AdminPanel: React.FC<AdminPanelProps> = ({ onLogout }) => {
  const [keywords, setKeywords] = useState<SDGKeyword[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSDG, setSelectedSDG] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [editingKeyword, setEditingKeyword] = useState<SDGKeyword | null>(null);
  const [newKeyword, setNewKeyword] = useState({ keyword: '', sdg: '1' });
  const [showAddForm, setShowAddForm] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [filteredCount, setFilteredCount] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(50); // Show 50 items per page
  const [totalPages, setTotalPages] = useState(1);

  // Load keywords with pagination
  const loadKeywords = async (page = 1) => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log(`🔄 Loading keywords page ${page} from SDG table...`);
      console.log(`🔍 Current filters - Search: "${searchQuery}", SDG: "${selectedSDG}"`);
      
      // Calculate offset for pagination
      const from = (page - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;
      
      // Build query with filters
      let query = supabase
        .from('sdg')
        .select('id, keyword, sdg, created_at, updated_at')
        .order('keyword', { ascending: true })
        .range(from, to);

      // Apply filters
      if (selectedSDG) {
        query = query.eq('sdg', selectedSDG);
        console.log(`🎯 Filtering by SDG: ${selectedSDG}`);
      }

      if (searchQuery.trim()) {
        query = query.ilike('keyword', `%${searchQuery.trim()}%`);
        console.log(`🔍 Searching for: "${searchQuery.trim()}"`);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Error loading keywords from SDG table:', error);
        setError(`Failed to load keywords: ${error.message}`);
        return;
      }

      console.log(`✅ Loaded ${data?.length || 0} keywords from SDG table (page ${page})`);
      setKeywords(data || []);
      
      // Get total count for pagination
      await updateCounts();
      
    } catch (err) {
      console.error('❌ Error in loadKeywords:', err);
      setError('Failed to load keywords from database');
    } finally {
      setIsLoading(false);
    }
  };

  // Update counts for pagination and display
  const updateCounts = async () => {
    try {
      console.log('📊 Updating counts...');
      
      // Get total count (unfiltered)
      const { count: totalCount } = await supabase
        .from('sdg')
        .select('*', { count: 'exact', head: true });
      
      setTotalCount(totalCount || 0);
      console.log(`📈 Total keywords (unfiltered): ${totalCount}`);

      // Get filtered count
      let countQuery = supabase
        .from('sdg')
        .select('*', { count: 'exact', head: true });

      if (selectedSDG) {
        countQuery = countQuery.eq('sdg', selectedSDG);
      }

      if (searchQuery.trim()) {
        countQuery = countQuery.ilike('keyword', `%${searchQuery.trim()}%`);
      }

      const { count: filteredCount } = await countQuery;
      
      setFilteredCount(filteredCount || 0);
      setTotalPages(Math.ceil((filteredCount || 0) / itemsPerPage));
      
      console.log(`📊 Filtered count: ${filteredCount}, Total pages: ${Math.ceil((filteredCount || 0) / itemsPerPage)}`);
      console.log(`🎯 Current filters active: Search="${searchQuery}", SDG="${selectedSDG}"`);
      
    } catch (err) {
      console.error('❌ Error updating counts:', err);
    }
  };

  // Load initial data
  useEffect(() => {
    loadKeywords(1);
  }, []);

  // Handle search and filter changes
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      console.log(`🔄 Filter changed - resetting to page 1. Search: "${searchQuery}", SDG: "${selectedSDG}"`);
      setCurrentPage(1); // Reset to first page when filters change
      loadKeywords(1);
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery, selectedSDG]);

  // Handle page changes
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      console.log(`📄 Changing to page ${newPage}`);
      setCurrentPage(newPage);
      loadKeywords(newPage);
    }
  };

  // Add new keyword to SDG table
  const handleAddKeyword = async () => {
    if (!newKeyword.keyword.trim()) {
      setError('Keyword cannot be empty');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('➕ Adding keyword to SDG table:', newKeyword);
      
      const { data, error } = await supabase
        .from('sdg')
        .insert([{
          keyword: newKeyword.keyword.trim(),
          sdg: newKeyword.sdg,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        console.error('❌ Error adding keyword to SDG table:', error);
        setError(`Failed to add keyword: ${error.message}`);
        return;
      }

      console.log('✅ Keyword added to SDG table successfully:', data);
      setSuccess('Keyword added successfully!');
      setNewKeyword({ keyword: '', sdg: '1' });
      setShowAddForm(false);
      
      // Refresh the current page and statistics
      await loadKeywords(currentPage);
      await refreshStatistics();
      
    } catch (err) {
      console.error('❌ Error in handleAddKeyword:', err);
      setError('Failed to add keyword');
    } finally {
      setIsLoading(false);
    }
  };

  // Update keyword in SDG table
  const handleUpdateKeyword = async () => {
    if (!editingKeyword || !editingKeyword.keyword.trim()) {
      setError('Keyword cannot be empty');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('✏️ Updating keyword in SDG table:', editingKeyword);
      
      const { error } = await supabase
        .from('sdg')
        .update({
          keyword: editingKeyword.keyword.trim(),
          sdg: editingKeyword.sdg
        })
        .eq('id', editingKeyword.id);

      if (error) {
        console.error('❌ Error updating keyword in SDG table:', error);
        setError(`Failed to update keyword: ${error.message}`);
        return;
      }

      console.log('✅ Keyword updated in SDG table successfully');
      setSuccess('Keyword updated successfully!');
      setEditingKeyword(null);
      
      // Refresh the current page and statistics
      await loadKeywords(currentPage);
      await refreshStatistics();
      
    } catch (err) {
      console.error('❌ Error in handleUpdateKeyword:', err);
      setError('Failed to update keyword');
    } finally {
      setIsLoading(false);
    }
  };

  // Delete keyword from SDG table
  const handleDeleteKeyword = async (id: string) => {
    if (!confirm('Are you sure you want to delete this keyword?')) {
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('🗑️ Deleting keyword from SDG table:', id);
      
      const { error } = await supabase
        .from('sdg')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Error deleting keyword from SDG table:', error);
        setError(`Failed to delete keyword: ${error.message}`);
        return;
      }

      console.log('✅ Keyword deleted from SDG table successfully');
      setSuccess('Keyword deleted successfully!');
      
      // Refresh the current page and statistics
      await loadKeywords(currentPage);
      await refreshStatistics();
      
    } catch (err) {
      console.error('❌ Error in handleDeleteKeyword:', err);
      setError('Failed to delete keyword');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      onLogout();
    } catch (err) {
      console.error('Error signing out:', err);
    }
  };

  // Force refresh statistics
  const handleRefreshStatistics = async () => {
    setIsRefreshing(true);
    try {
      console.log('🔄 Manually refreshing statistics...');
      await refreshStatistics();
      await loadKeywords(currentPage);
      setSuccess('Statistics refreshed successfully!');
    } catch (err) {
      console.error('❌ Error refreshing statistics:', err);
      setError('Failed to refresh statistics.');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Clear all filters
  const handleClearFilters = () => {
    console.log('🧹 Clearing all filters');
    setSearchQuery('');
    setSelectedSDG('');
    setCurrentPage(1);
    // The useEffect will trigger loadKeywords automatically
  };

  // Clear messages after 5 seconds
  useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess(null);
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success, error]);

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const start = Math.max(1, currentPage - 2);
      const end = Math.min(totalPages, start + maxVisiblePages - 1);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }
    
    return pages;
  };

  // Check if filters are active
  const hasActiveFilters = searchQuery.trim() || selectedSDG;

  // Pagination component
  const PaginationControls = () => (
    <div className="flex items-center gap-2">
      {/* Previous Button */}
      <button
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <ChevronLeft className="w-4 h-4" />
        Previous
      </button>
      
      {/* Page Numbers */}
      <div className="flex gap-1">
        {getPageNumbers().map((pageNum) => (
          <button
            key={pageNum}
            onClick={() => handlePageChange(pageNum)}
            className={`px-3 py-2 text-sm rounded-lg ${
              pageNum === currentPage
                ? 'bg-blue-600 text-white'
                : 'border border-gray-300 hover:bg-gray-100'
            }`}
          >
            {pageNum}
          </button>
        ))}
      </div>
      
      {/* Next Button */}
      <button
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Next
        <ChevronRight className="w-4 h-4" />
      </button>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Database className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">SDG Keywords Admin Panel</h1>
                <p className="text-sm text-gray-600">
                  Managing {totalCount.toLocaleString()} keywords in SDG table
                  {hasActiveFilters && (
                    <span className="ml-2 text-blue-600">
                      (showing {filteredCount.toLocaleString()} filtered results)
                    </span>
                  )}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {/* Refresh Button */}
              <button
                onClick={handleRefreshStatistics}
                disabled={isRefreshing}
                className="flex items-center gap-2 px-3 py-2 text-green-600 hover:text-green-700 font-medium text-sm border border-green-200 rounded-lg hover:bg-green-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Refresh statistics"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                {isRefreshing ? 'Refreshing...' : 'Refresh'}
              </button>
              
              <button
                onClick={handleLogout}
                className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-6">
        {/* Success/Error Messages */}
        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-700">
              <CheckCircle className="w-5 h-5" />
              <span className="font-medium">Success</span>
            </div>
            <p className="text-green-600 mt-1">{success}</p>
          </div>
        )}

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="w-5 h-5" />
              <span className="font-medium">Error</span>
            </div>
            <p className="text-red-600 mt-1">{error}</p>
          </div>
        )}

        {/* Keywords Controls */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="flex flex-wrap items-center gap-4 mb-4">
            {/* Search */}
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search keywords..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* SDG Filter */}
            <select
              value={selectedSDG}
              onChange={(e) => setSelectedSDG(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All SDGs</option>
              {Array.from({ length: 17 }, (_, i) => (i + 1).toString()).map(num => (
                <option key={num} value={num}>
                  SDG {num}: {SDG_NAMES[num]}
                </option>
              ))}
            </select>

            {/* Clear Filters Button */}
            {hasActiveFilters && (
              <button
                onClick={handleClearFilters}
                className="flex items-center gap-2 px-4 py-2 bg-red-50 text-red-700 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
              >
                <X className="w-4 h-4" />
                Clear All Filters
              </button>
            )}

            {/* Add Keyword Button */}
            <button
              onClick={() => setShowAddForm(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              Add Keyword
            </button>
          </div>

          {/* Add Keyword Form */}
          {showAddForm && (
            <div className="border-t pt-4">
              <h3 className="text-lg font-semibold mb-4">Add New Keyword</h3>
              <div className="flex gap-4">
                <input
                  type="text"
                  value={newKeyword.keyword}
                  onChange={(e) => setNewKeyword({ ...newKeyword, keyword: e.target.value })}
                  placeholder="Enter keyword..."
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <select
                  value={newKeyword.sdg}
                  onChange={(e) => setNewKeyword({ ...newKeyword, sdg: e.target.value })}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {Array.from({ length: 17 }, (_, i) => (i + 1).toString()).map(num => (
                    <option key={num} value={num}>
                      SDG {num}
                    </option>
                  ))}
                </select>
                <button
                  onClick={handleAddKeyword}
                  disabled={isLoading || !newKeyword.keyword.trim()}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Save className="w-4 h-4" />
                  Save
                </button>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    setNewKeyword({ keyword: '', sdg: '1' });
                  }}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <X className="w-4 h-4" />
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Keywords Table */}
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div className="px-6 py-4 border-b bg-gray-50">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">
                Keywords (Page {currentPage} of {totalPages})
              </h2>
              
              {/* Top Navigation Controls */}
              {totalPages > 1 && (
                <PaginationControls />
              )}
            </div>
            
            {/* Results info below the header */}
            <div className="mt-2 text-sm text-gray-600">
              Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, filteredCount)} of {filteredCount.toLocaleString()} 
              {hasActiveFilters ? ' filtered' : ''} results
            </div>
          </div>

          {isLoading ? (
            <div className="p-8 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-200 border-t-blue-600"></div>
              <p className="text-gray-600 mt-2">Loading keywords from SDG table...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Keyword
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      SDG
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {keywords.map((keyword) => (
                    <tr key={keyword.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        {editingKeyword?.id === keyword.id ? (
                          <input
                            type="text"
                            value={editingKeyword.keyword}
                            onChange={(e) => setEditingKeyword({ ...editingKeyword, keyword: e.target.value })}
                            className="w-full px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        ) : (
                          <span className="text-sm text-gray-900">{keyword.keyword}</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {editingKeyword?.id === keyword.id ? (
                          <select
                            value={editingKeyword.sdg}
                            onChange={(e) => setEditingKeyword({ ...editingKeyword, sdg: e.target.value })}
                            className="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            {Array.from({ length: 17 }, (_, i) => (i + 1).toString()).map(num => (
                              <option key={num} value={num}>
                                SDG {num}
                              </option>
                            ))}
                          </select>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            SDG {keyword.sdg}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {keyword.created_at ? new Date(keyword.created_at).toLocaleDateString() : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {editingKeyword?.id === keyword.id ? (
                          <div className="flex gap-2">
                            <button
                              onClick={handleUpdateKeyword}
                              disabled={isLoading}
                              className="text-green-600 hover:text-green-900 disabled:opacity-50"
                            >
                              <Save className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => setEditingKeyword(null)}
                              className="text-gray-600 hover:text-gray-900"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        ) : (
                          <div className="flex gap-2">
                            <button
                              onClick={() => setEditingKeyword(keyword)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteKeyword(keyword.id)}
                              disabled={isLoading}
                              className="text-red-600 hover:text-red-900 disabled:opacity-50"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {keywords.length === 0 && !isLoading && (
                <div className="p-8 text-center text-gray-500">
                  {hasActiveFilters
                    ? 'No keywords found matching your criteria.'
                    : 'No keywords found in the database.'
                  }
                </div>
              )}
            </div>
          )}

          {/* Bottom Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredCount)} of {filteredCount.toLocaleString()} results
                </div>
                
                <PaginationControls />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPanel;