import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export interface SDGKeyword {
  id: string;
  keyword: string;
  sdg: string;
  created_at?: string;
  updated_at?: string;
}

export interface KeywordFrequency {
  keyword: string;
  count: number;
  sdgs: string[];
}

export const SDG_NAMES: { [key: string]: string } = {
  '1': 'No Poverty',
  '2': 'Zero Hunger',
  '3': 'Good Health and Well-being',
  '4': 'Quality Education',
  '5': 'Gender Equality',
  '6': 'Clean Water and Sanitation',
  '7': 'Affordable and Clean Energy',
  '8': 'Decent Work and Economic Growth',
  '9': 'Industry, Innovation and Infrastructure',
  '10': 'Reduced Inequalities',
  '11': 'Sustainable Cities and Communities',
  '12': 'Responsible Consumption and Production',
  '13': 'Climate Action',
  '14': 'Life Below Water',
  '15': 'Life on Land',
  '16': 'Peace, Justice and Strong Institutions',
  '17': 'Partnerships for the Goals'
};

export type SortOption = 'alphabetical' | 'sdg' | 'length';

// Helper function to escape special regex characters
const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

// Helper function to check if a keyword is relevant to the search term
const isRelevantMatch = (keyword: string, searchTerm: string): boolean => {
  const keywordLower = keyword.toLowerCase();
  const searchLower = searchTerm.toLowerCase();
  
  // Exact match
  if (keywordLower === searchLower) {
    return true;
  }
  
  // Check if search term appears as a complete word (surrounded by word boundaries)
  const wordBoundaryRegex = new RegExp(`\\b${escapeRegExp(searchLower)}\\b`, 'i');
  if (wordBoundaryRegex.test(keyword)) {
    return true;
  }
  
  // Check if keyword starts with search term followed by common word separators
  const startsWithRegex = new RegExp(`^${escapeRegExp(searchLower)}[\\s\\-_.,;:()]`, 'i');
  if (startsWithRegex.test(keyword)) {
    return true;
  }
  
  // Check if keyword ends with search term preceded by common word separators
  const endsWithRegex = new RegExp(`[\\s\\-_.,;:()]${escapeRegExp(searchLower)}$`, 'i');
  if (endsWithRegex.test(keyword)) {
    return true;
  }
  
  // For compound words, check if search term appears at the start of the keyword
  if (keywordLower.startsWith(searchLower)) {
    // But only if the next character is not a letter (to avoid partial matches)
    const nextChar = keyword[searchTerm.length];
    if (nextChar && /[^a-zA-Z]/.test(nextChar)) {
      return true;
    }
    // Or if it's a compound word pattern (like "womanhood", "womankind")
    if (nextChar && /[A-Z]/.test(nextChar)) {
      return true;
    }
  }
  
  // For multi-word search terms, check if the entire phrase exists
  if (searchTerm.includes(' ') && keywordLower.includes(searchLower)) {
    return true;
  }
  
  // Check for plural/singular forms and common variations
  if (searchLower.endsWith('s') && keywordLower.includes(searchLower.slice(0, -1))) {
    const singular = searchLower.slice(0, -1);
    const singularWordBoundary = new RegExp(`\\b${escapeRegExp(singular)}\\b`, 'i');
    return singularWordBoundary.test(keyword);
  }
  
  if (!searchLower.endsWith('s') && keywordLower.includes(searchLower + 's')) {
    const plural = searchLower + 's';
    const pluralWordBoundary = new RegExp(`\\b${escapeRegExp(plural)}\\b`, 'i');
    return pluralWordBoundary.test(keyword);
  }
  
  return false;
};

// Enhanced keyword search - USING SDG TABLE
export const searchKeywords = async (
  query: string, 
  sdgFilter?: string, 
  sortBy: SortOption = 'alphabetical'
): Promise<SDGKeyword[]> => {
  try {
    if (!query.trim() && !sdgFilter) {
      return [];
    }

    let allResults: SDGKeyword[] = [];
    let from = 0;
    const pageSize = 1000;
    let hasMore = true;

    while (hasMore) {
      let queryBuilder = supabase
        .from('sdg') // USING SDG TABLE
        .select('id, keyword, sdg')
        .range(from, from + pageSize - 1);

      // Apply SDG filter first
      if (sdgFilter) {
        queryBuilder = queryBuilder.eq('sdg', sdgFilter);
      }

      // For database-level filtering, use a broader search to get candidates
      // We'll do precise filtering on the client side
      if (query.trim()) {
        const searchTerm = query.trim();
        queryBuilder = queryBuilder.ilike('keyword', `%${searchTerm}%`);
      }

      // Apply sorting
      switch (sortBy) {
        case 'alphabetical':
          queryBuilder = queryBuilder.order('keyword', { ascending: true });
          break;
        case 'sdg':
          queryBuilder = queryBuilder.order('sdg', { ascending: true }).order('keyword', { ascending: true });
          break;
        case 'length':
          queryBuilder = queryBuilder.order('keyword', { ascending: true });
          break;
      }

      const { data, error } = await queryBuilder;

      if (error) {
        console.error('Error searching keywords:', error);
        break;
      }

      if (!data || data.length === 0) {
        hasMore = false;
      } else {
        // Apply precise client-side filtering
        let filteredData = data;
        
        if (query.trim()) {
          const searchTerm = query.trim();
          filteredData = data.filter(item => {
            return isRelevantMatch(item.keyword, searchTerm);
          });
        }
        
        allResults = allResults.concat(filteredData);
        
        // If we got less than pageSize, we've reached the end
        if (data.length < pageSize) {
          hasMore = false;
        } else {
          from += pageSize;
        }
      }
    }

    // Remove duplicates
    const uniqueResults = allResults.filter((item, index, self) => 
      index === self.findIndex(t => t.id === item.id)
    );

    // Apply client-side sorting for length if needed
    if (sortBy === 'length') {
      uniqueResults.sort((a, b) => a.keyword.length - b.keyword.length);
    }

    // Sort results by relevance if there's a search query
    if (query.trim()) {
      const searchTerm = query.trim().toLowerCase();
      uniqueResults.sort((a, b) => {
        const aKeyword = a.keyword.toLowerCase();
        const bKeyword = b.keyword.toLowerCase();
        
        // Exact matches first
        if (aKeyword === searchTerm && bKeyword !== searchTerm) return -1;
        if (bKeyword === searchTerm && aKeyword !== searchTerm) return 1;
        
        // Starts with search term (for compound words)
        const aStartsWith = aKeyword.startsWith(searchTerm);
        const bStartsWith = bKeyword.startsWith(searchTerm);
        if (aStartsWith && !bStartsWith) return -1;
        if (bStartsWith && !aStartsWith) return 1;
        
        // Word boundary matches (whole words)
        const aWholeWord = new RegExp(`\\b${escapeRegExp(searchTerm)}\\b`, 'i').test(a.keyword);
        const bWholeWord = new RegExp(`\\b${escapeRegExp(searchTerm)}\\b`, 'i').test(b.keyword);
        
        if (aWholeWord && !bWholeWord) return -1;
        if (bWholeWord && !aWholeWord) return 1;
        
        // Shorter keywords first (more specific)
        return a.keyword.length - b.keyword.length;
      });
    }

    console.log(`Total search results fetched from 'sdg' table: ${uniqueResults.length}`);
    return uniqueResults;
  } catch (error) {
    console.error('Error in searchKeywords:', error);
    return [];
  }
};

export const getKeywordsBySDG = async (sdg: string, sortBy: SortOption = 'alphabetical'): Promise<SDGKeyword[]> => {
  try {
    let allResults: SDGKeyword[] = [];
    let from = 0;
    const pageSize = 1000;
    let hasMore = true;

    while (hasMore) {
      let queryBuilder = supabase
        .from('sdg') // USING SDG TABLE
        .select('id, keyword, sdg')
        .eq('sdg', sdg)
        .range(from, from + pageSize - 1);

      // Apply sorting
      switch (sortBy) {
        case 'alphabetical':
          queryBuilder = queryBuilder.order('keyword', { ascending: true });
          break;
        case 'length':
          queryBuilder = queryBuilder.order('keyword', { ascending: true });
          break;
        case 'sdg':
          queryBuilder = queryBuilder.order('keyword', { ascending: true });
          break;
      }

      const { data, error } = await queryBuilder;

      if (error) {
        console.error('Error fetching keywords by SDG:', error);
        break;
      }

      if (!data || data.length === 0) {
        hasMore = false;
      } else {
        allResults = allResults.concat(data);
        
        if (data.length < pageSize) {
          hasMore = false;
        } else {
          from += pageSize;
        }
      }
    }

    // Apply client-side sorting for length if needed
    if (sortBy === 'length') {
      allResults = allResults.sort((a, b) => a.keyword.length - b.keyword.length);
    }

    console.log(`Total keywords fetched for SDG ${sdg} from 'sdg' table: ${allResults.length}`);
    return allResults;
  } catch (error) {
    console.error('Error in getKeywordsBySDG:', error);
    return [];
  }
};

// Get details for a specific keyword (all occurrences across SDGs) - USING SDG TABLE
export const getKeywordDetails = async (keyword: string): Promise<SDGKeyword[]> => {
  try {
    let allResults: SDGKeyword[] = [];
    let from = 0;
    const pageSize = 1000;
    let hasMore = true;

    while (hasMore) {
      const { data, error } = await supabase
        .from('sdg') // USING SDG TABLE
        .select('id, keyword, sdg')
        .ilike('keyword', keyword) // Exact match (case insensitive)
        .order('sdg', { ascending: true })
        .range(from, from + pageSize - 1);

      if (error) {
        console.error('Error fetching keyword details:', error);
        break;
      }

      if (!data || data.length === 0) {
        hasMore = false;
      } else {
        allResults = allResults.concat(data);
        
        if (data.length < pageSize) {
          hasMore = false;
        } else {
          from += pageSize;
        }
      }
    }

    console.log(`Keyword details for "${keyword}" from 'sdg' table: ${allResults.length} occurrences`);
    return allResults;
  } catch (error) {
    console.error('Error in getKeywordDetails:', error);
    return [];
  }
};

// Get accurate keyword count by SDG using pagination to fetch ALL records - USING SDG TABLE
export const getKeywordCountBySDG = async (): Promise<{ [key: string]: number }> => {
  try {
    console.log('Fetching all keywords for counting from sdg table...');
    
    let allKeywords: { sdg: string }[] = [];
    let from = 0;
    const pageSize = 1000;
    let hasMore = true;

    // Fetch ALL keywords in batches
    while (hasMore) {
      const { data, error } = await supabase
        .from('sdg') // USING SDG TABLE
        .select('sdg')
        .range(from, from + pageSize - 1);

      if (error) {
        console.error('Error fetching keywords for counting:', error);
        break;
      }

      if (!data || data.length === 0) {
        hasMore = false;
      } else {
        allKeywords = allKeywords.concat(data);
        
        if (data.length < pageSize) {
          hasMore = false;
        } else {
          from += pageSize;
        }
      }
    }

    console.log(`Total keywords fetched for counting from 'sdg' table: ${allKeywords.length}`);

    const counts: { [key: string]: number } = {};
    
    // Initialize all SDGs with 0
    for (let i = 1; i <= 17; i++) {
      counts[i.toString()] = 0;
    }

    // Count actual keywords
    allKeywords.forEach(item => {
      if (item.sdg) {
        counts[item.sdg] = (counts[item.sdg] || 0) + 1;
      }
    });

    console.log('SDG keyword counts from sdg table:', counts);
    return counts;
  } catch (error) {
    console.error('Error in getKeywordCountBySDG:', error);
    return {};
  }
};

// Get total keyword count using exact count - USING SDG TABLE
export const getTotalKeywordCount = async (bustCache: boolean = false): Promise<number> => {
  try {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(7);
    console.log(`🔄 Fetching ${bustCache ? 'CACHE-BUSTED' : 'normal'} total keyword count from 'sdg' table at ${timestamp} (ID: ${randomId})`);
    
    // Create a completely new Supabase client instance to avoid any caching
    const freshClient = bustCache ? createClient(
      import.meta.env.VITE_SUPABASE_URL,
      import.meta.env.VITE_SUPABASE_ANON_KEY,
      {
        db: {
          schema: 'public'
        },
        global: {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'X-Timestamp': timestamp.toString(),
            'X-Random': randomId
          }
        }
      }
    ) : supabase;

    const { count, error } = await freshClient
      .from('sdg') // USING SDG TABLE
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Error fetching total keyword count from sdg table:', error);
      return 0;
    }

    const totalCount = count || 0;
    console.log(`✅ ${bustCache ? 'CACHE-BUSTED' : 'Normal'} total keyword count from 'sdg' table: ${totalCount} at ${timestamp} (ID: ${randomId})`);
    return totalCount;
  } catch (error) {
    console.error('❌ Error in getTotalKeywordCount:', error);
    return 0;
  }
};

// Get most frequent WHOLE PHRASES/KEYWORDS (not individual words) - Show top 10 - USING SDG TABLE
export const getTopFrequencyWords = async (limit: number = 10): Promise<KeywordFrequency[]> => {
  try {
    console.log('Fetching all keywords for phrase frequency analysis from sdg table...');
    
    let allKeywords: { keyword: string; sdg: string }[] = [];
    let from = 0;
    const pageSize = 1000;
    let hasMore = true;

    // Fetch ALL keywords in batches
    while (hasMore) {
      const { data, error } = await supabase
        .from('sdg') // USING SDG TABLE
        .select('keyword, sdg')
        .range(from, from + pageSize - 1);

      if (error) {
        console.error('Error fetching keywords for frequency analysis:', error);
        break;
      }

      if (!data || data.length === 0) {
        hasMore = false;
      } else {
        allKeywords = allKeywords.concat(data);
        
        if (data.length < pageSize) {
          hasMore = false;
        } else {
          from += pageSize;
        }
      }
    }

    console.log(`Total keywords fetched for phrase frequency analysis from 'sdg' table: ${allKeywords.length}`);

    if (allKeywords.length === 0) return [];

    // Count frequency of WHOLE PHRASES/KEYWORDS (not individual words)
    const phraseFrequencyMap = new Map<string, { count: number; sdgs: Set<string> }>();

    allKeywords.forEach(item => {
      // Skip items with null, undefined, or empty keywords
      if (!item.keyword || typeof item.keyword !== 'string' || item.keyword.trim() === '') {
        return;
      }

      // Use the whole keyword/phrase as-is, just normalize case
      const phrase = item.keyword.toLowerCase().trim();
      
      if (!phraseFrequencyMap.has(phrase)) {
        phraseFrequencyMap.set(phrase, { count: 0, sdgs: new Set() });
      }
      const entry = phraseFrequencyMap.get(phrase)!;
      entry.count += 1;
      entry.sdgs.add(item.sdg);
    });

    // Convert to array and sort by frequency - Show top 10
    const frequencies: KeywordFrequency[] = Array.from(phraseFrequencyMap.entries())
      .map(([phrase, data]) => ({
        keyword: phrase,
        count: data.count,
        sdgs: Array.from(data.sdgs).sort((a, b) => parseInt(a) - parseInt(b))
      }))
      .filter(item => item.count >= 1) // Show all phrases (since each should appear at least once)
      .sort((a, b) => {
        // Sort by count first, then by number of SDGs, then alphabetically
        if (b.count !== a.count) return b.count - a.count;
        if (b.sdgs.length !== a.sdgs.length) return b.sdgs.length - a.sdgs.length;
        return a.keyword.localeCompare(b.keyword);
      })
      .slice(0, limit); // Show top 10

    console.log(`Top ${limit} phrase frequencies calculated from 'sdg' table:`, frequencies);
    return frequencies;
  } catch (error) {
    console.error('Error in getTopFrequencyWords:', error);
    return [];
  }
};

// Get keyword statistics with forced refresh and ultra cache busting
export const getKeywordStatistics = async (forceRefresh: boolean = false) => {
  try {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(7);
    console.log(`🔄 Loading ${forceRefresh ? 'FORCE-REFRESHED' : 'normal'} comprehensive keyword statistics from 'sdg' table... at ${timestamp} (ID: ${randomId})`);
    
    const [totalCount, countsBySDG, topFrequencies] = await Promise.all([
      getTotalKeywordCount(forceRefresh),
      getKeywordCountBySDG(),
      getTopFrequencyWords(10) // Show top 10 phrases
    ]);

    // Calculate additional statistics
    const sdgCounts = Object.values(countsBySDG);
    const avgKeywordsPerSDG = totalCount > 0 ? Math.round(totalCount / 17) : 0;
    const maxKeywordsInSDG = Math.max(...sdgCounts, 0);
    const minKeywordsInSDG = Math.min(...sdgCounts.filter(count => count > 0), 0);

    const stats = {
      totalKeywords: totalCount,
      keywordsBySDG: countsBySDG,
      topFrequencies,
      avgKeywordsPerSDG,
      maxKeywordsInSDG,
      minKeywordsInSDG,
      sdgsWithKeywords: 17, // Always 17 SDGs total
      lastRefreshed: timestamp,
      refreshId: randomId,
      cacheStatus: forceRefresh ? 'CACHE_BUSTED' : 'NORMAL',
      tableUsed: 'sdg' // Track which table is being used
    };

    console.log(`✅ ${forceRefresh ? 'FORCE-REFRESHED' : 'Normal'} statistics from 'sdg' table at ${timestamp} (ID: ${randomId}):`, stats);
    return stats;
  } catch (error) {
    console.error('❌ Error getting keyword statistics:', error);
    return {
      totalKeywords: 0,
      keywordsBySDG: {},
      topFrequencies: [],
      avgKeywordsPerSDG: 0,
      maxKeywordsInSDG: 0,
      minKeywordsInSDG: 0,
      sdgsWithKeywords: 17,
      lastRefreshed: Date.now(),
      refreshId: Math.random().toString(36).substring(7),
      cacheStatus: 'ERROR',
      tableUsed: 'sdg'
    };
  }
};

// Test database connection - USING SDG TABLE
export const testDatabaseConnection = async (): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('sdg') // USING SDG TABLE
      .select('id', { count: 'exact', head: true })
      .limit(1);

    if (error) {
      console.error('Database connection test failed for sdg table:', error);
      return false;
    }

    console.log('Database connection successful to sdg table');
    return true;
  } catch (error) {
    console.error('Database connection test error:', error);
    return false;
  }
};

// Get last updated date for the database - using updated_at to reflect actual changes - USING SDG TABLE
export const getLastUpdatedDate = async (): Promise<Date | null> => {
  try {
    const { data, error } = await supabase
      .from('sdg') // USING SDG TABLE
      .select('updated_at')
      .order('updated_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error('Error fetching last updated date from sdg table:', error);
      return null;
    }

    if (!data || !data.updated_at) {
      // If no updated_at data, fall back to created_at
      console.log('No updated_at found, falling back to created_at...');
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('sdg')
        .select('created_at')
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (fallbackError || !fallbackData) {
        console.log('No records found in sdg table for last updated date');
        return null;
      }

      return new Date(fallbackData.created_at);
    }

    return new Date(data.updated_at);
  } catch (error) {
    console.error('Error in getLastUpdatedDate:', error);
    return null;
  }
};

// Force refresh statistics - useful after deletions
export const refreshStatistics = async () => {
  console.log('🔄 Force refreshing statistics after data changes from sdg table...');
  return await getKeywordStatistics(true);
};