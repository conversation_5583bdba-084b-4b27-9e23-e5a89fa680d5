/*
  # Check row counts and analyze both SDG tables

  1. Analysis
    - Count rows in both sdg and sdg_keywords tables
    - Check for duplicates in sdg table
    - Show sample data from both tables
    - Display breakdown by SDG for both tables

  2. Purpose
    - Understand why sdg table has 22,031 keywords
    - Verify if this is expected or if there are issues
    - Help determine next steps for data cleanup
*/

-- Check row counts and duplicates in both tables
DO $$
DECLARE
  sdg_count INTEGER;
  sdg_keywords_count INTEGER;
  sdg_sample RECORD;
  sdg_keywords_sample RECORD;
  duplicate_count INTEGER;
BEGIN
  -- Get counts
  SELECT COUNT(*) INTO sdg_count FROM sdg;
  SELECT COUNT(*) INTO sdg_keywords_count FROM sdg_keywords;
  
  -- Get sample records
  SELECT keyword, sdg INTO sdg_sample FROM sdg LIMIT 1;
  SELECT keyword, sdg INTO sdg_keywords_sample FROM sdg_keywords LIMIT 1;
  
  RAISE NOTICE 'SDG table count: %', sdg_count;
  RAISE NOTICE 'SDG_KEYWORDS table count: %', sdg_keywords_count;
  RAISE NOTICE 'SDG sample: keyword=%, sdg=%', sdg_sample.keyword, sdg_sample.sdg;
  RAISE NOTICE 'SDG_KEYWORDS sample: keyword=%, sdg=%', sdg_keywords_sample.keyword, sdg_keywords_sample.sdg;
  
  -- Check for duplicates in sdg table
  WITH duplicate_check AS (
    SELECT keyword, sdg, COUNT(*) as count
    FROM sdg
    GROUP BY keyword, sdg
    HAVING COUNT(*) > 1
  )
  SELECT COUNT(*) INTO duplicate_count FROM duplicate_check;
  
  RAISE NOTICE 'Duplicate keyword-sdg combinations in SDG table: %', duplicate_count;
END $$;

-- Create a temporary view to show breakdown by SDG for both tables
CREATE OR REPLACE VIEW temp_sdg_breakdown AS
SELECT 
  'sdg' as table_name,
  sdg,
  COUNT(*) as keyword_count
FROM sdg
GROUP BY sdg

UNION ALL

SELECT 
  'sdg_keywords' as table_name,
  sdg,
  COUNT(*) as keyword_count
FROM sdg_keywords
GROUP BY sdg;

-- Display the breakdown
SELECT * FROM temp_sdg_breakdown ORDER BY table_name, sdg::integer;

-- Clean up the temporary view
DROP VIEW IF EXISTS temp_sdg_breakdown;

-- Additional analysis: Check if there are any null values
DO $$
DECLARE
  sdg_nulls INTEGER;
  sdg_keywords_nulls INTEGER;
BEGIN
  SELECT COUNT(*) INTO sdg_nulls FROM sdg WHERE keyword IS NULL OR sdg IS NULL;
  SELECT COUNT(*) INTO sdg_keywords_nulls FROM sdg_keywords WHERE keyword IS NULL OR sdg IS NULL;
  
  RAISE NOTICE 'NULL values in SDG table: %', sdg_nulls;
  RAISE NOTICE 'NULL values in SDG_KEYWORDS table: %', sdg_keywords_nulls;
END $$;