/*
  # Check keyword counts in both tables

  1. Analysis
    - Count total keywords in both sdg and sdg_keywords tables
    - Show breakdown by SDG for each table
    - Check for duplicates and data quality issues
    - Compare the two tables to understand differences

  2. Output
    - Total counts for both tables
    - Per-SDG breakdown
    - Duplicate analysis
    - Sample data comparison
*/

-- Check total counts and basic info
DO $$
DECLARE
  sdg_count INTEGER;
  sdg_keywords_count INTEGER;
  sdg_unique_count INTEGER;
  sdg_keywords_unique_count INTEGER;
BEGIN
  -- Get total counts
  SELECT COUNT(*) INTO sdg_count FROM sdg;
  SELECT COUNT(*) INTO sdg_keywords_count FROM sdg_keywords;
  
  -- Get unique keyword-sdg combination counts
  SELECT COUNT(DISTINCT (keyword, sdg)) INTO sdg_unique_count FROM sdg;
  SELECT COUNT(DISTINCT (keyword, sdg)) INTO sdg_keywords_unique_count FROM sdg_keywords;
  
  RAISE NOTICE '=== TABLE COMPARISON ===';
  RAISE NOTICE 'SDG table: % total rows, % unique keyword-sdg combinations', sdg_count, sdg_unique_count;
  RAISE NOTICE 'SDG_KEYWORDS table: % total rows, % unique keyword-sdg combinations', sdg_keywords_count, sdg_keywords_unique_count;
  RAISE NOTICE 'Difference: % rows (sdg table has % more/less)', sdg_count - sdg_keywords_count, sdg_count - sdg_keywords_count;
END $$;

-- Show breakdown by SDG for both tables
DO $$
DECLARE
  rec RECORD;
BEGIN
  RAISE NOTICE '=== BREAKDOWN BY SDG ===';
  RAISE NOTICE 'SDG | SDG_Table | SDG_Keywords_Table | Difference';
  RAISE NOTICE '----+----------+-------------------+-----------';
  
  FOR rec IN 
    SELECT 
      COALESCE(s.sdg, sk.sdg) as sdg_num,
      COALESCE(s.count, 0) as sdg_count,
      COALESCE(sk.count, 0) as sdg_keywords_count,
      COALESCE(s.count, 0) - COALESCE(sk.count, 0) as difference
    FROM (
      SELECT sdg, COUNT(*) as count FROM sdg GROUP BY sdg
    ) s
    FULL OUTER JOIN (
      SELECT sdg, COUNT(*) as count FROM sdg_keywords GROUP BY sdg
    ) sk ON s.sdg = sk.sdg
    ORDER BY COALESCE(s.sdg, sk.sdg)::integer
  LOOP
    RAISE NOTICE '% | % | % | %', 
      LPAD(rec.sdg_num, 3), 
      LPAD(rec.sdg_count::text, 8), 
      LPAD(rec.sdg_keywords_count::text, 17), 
      LPAD(rec.difference::text, 9);
  END LOOP;
END $$;

-- Check for duplicates in both tables
DO $$
DECLARE
  sdg_duplicates INTEGER;
  sdg_keywords_duplicates INTEGER;
BEGIN
  -- Count duplicates in sdg table
  WITH sdg_dups AS (
    SELECT keyword, sdg, COUNT(*) as count
    FROM sdg
    GROUP BY keyword, sdg
    HAVING COUNT(*) > 1
  )
  SELECT COUNT(*) INTO sdg_duplicates FROM sdg_dups;
  
  -- Count duplicates in sdg_keywords table
  WITH sdg_keywords_dups AS (
    SELECT keyword, sdg, COUNT(*) as count
    FROM sdg_keywords
    GROUP BY keyword, sdg
    HAVING COUNT(*) > 1
  )
  SELECT COUNT(*) INTO sdg_keywords_duplicates FROM sdg_keywords_dups;
  
  RAISE NOTICE '=== DUPLICATE ANALYSIS ===';
  RAISE NOTICE 'SDG table duplicate keyword-sdg combinations: %', sdg_duplicates;
  RAISE NOTICE 'SDG_KEYWORDS table duplicate keyword-sdg combinations: %', sdg_keywords_duplicates;
END $$;

-- Show sample data from both tables
DO $$
DECLARE
  rec RECORD;
  counter INTEGER := 0;
BEGIN
  RAISE NOTICE '=== SAMPLE DATA COMPARISON ===';
  RAISE NOTICE 'First 5 records from each table:';
  RAISE NOTICE '';
  RAISE NOTICE 'SDG Table:';
  FOR rec IN SELECT keyword, sdg FROM sdg ORDER BY keyword LIMIT 5 LOOP
    counter := counter + 1;
    RAISE NOTICE '  %: "%" (SDG %)', counter, rec.keyword, rec.sdg;
  END LOOP;
  
  RAISE NOTICE '';
  RAISE NOTICE 'SDG_KEYWORDS Table:';
  counter := 0;
  FOR rec IN SELECT keyword, sdg FROM sdg_keywords ORDER BY keyword LIMIT 5 LOOP
    counter := counter + 1;
    RAISE NOTICE '  %: "%" (SDG %)', counter, rec.keyword, rec.sdg;
  END LOOP;
END $$;

-- Check for any data quality issues
DO $$
DECLARE
  sdg_nulls INTEGER;
  sdg_keywords_nulls INTEGER;
  sdg_empty INTEGER;
  sdg_keywords_empty INTEGER;
BEGIN
  -- Check for null values
  SELECT COUNT(*) INTO sdg_nulls FROM sdg WHERE keyword IS NULL OR sdg IS NULL;
  SELECT COUNT(*) INTO sdg_keywords_nulls FROM sdg_keywords WHERE keyword IS NULL OR sdg IS NULL;
  
  -- Check for empty/whitespace keywords
  SELECT COUNT(*) INTO sdg_empty FROM sdg WHERE TRIM(keyword) = '';
  SELECT COUNT(*) INTO sdg_keywords_empty FROM sdg_keywords WHERE TRIM(keyword) = '';
  
  RAISE NOTICE '=== DATA QUALITY CHECK ===';
  RAISE NOTICE 'SDG table - NULL values: %, Empty keywords: %', sdg_nulls, sdg_empty;
  RAISE NOTICE 'SDG_KEYWORDS table - NULL values: %, Empty keywords: %', sdg_keywords_nulls, sdg_keywords_empty;
END $$;