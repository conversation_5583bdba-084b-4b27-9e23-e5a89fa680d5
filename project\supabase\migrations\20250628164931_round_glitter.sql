/*
  # Create admin user account

  1. New User Creation
    - Creates admin user with email: <EMAIL>
    - Sets password: Wpgcs202505!
    - Confirms email automatically
    - Creates corresponding identity record with proper provider_id

  2. Security
    - Uses proper password hashing with bcrypt
    - Sets up email provider identity
    - Ensures user can log in immediately
*/

DO $$
DECLARE
    new_user_id uuid;
BEGIN
    -- Check if user already exists
    IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
        -- Generate new user ID
        new_user_id := gen_random_uuid();
        
        -- Insert admin user into auth.users table
        INSERT INTO auth.users (
            instance_id,
            id,
            aud,
            role,
            email,
            encrypted_password,
            email_confirmed_at,
            invited_at,
            confirmation_token,
            confirmation_sent_at,
            recovery_token,
            recovery_sent_at,
            email_change_token_new,
            email_change,
            email_change_sent_at,
            last_sign_in_at,
            raw_app_meta_data,
            raw_user_meta_data,
            is_super_admin,
            created_at,
            updated_at,
            phone,
            phone_confirmed_at,
            phone_change,
            phone_change_token,
            phone_change_sent_at,
            email_change_token_current,
            email_change_confirm_status,
            banned_until,
            reauthentication_token,
            reauthentication_sent_at,
            is_sso_user,
            deleted_at
        ) VALUES (
            '00000000-0000-0000-0000-000000000000',
            new_user_id,
            'authenticated',
            'authenticated',
            '<EMAIL>',
            crypt('Wpgcs202505!', gen_salt('bf')),
            now(),
            now(),
            '',
            now(),
            '',
            null,
            '',
            '',
            null,
            null,
            '{"provider": "email", "providers": ["email"]}',
            '{}',
            false,
            now(),
            now(),
            null,
            null,
            '',
            '',
            null,
            '',
            0,
            null,
            '',
            null,
            false,
            null
        );

        -- Insert corresponding identity record with provider_id
        INSERT INTO auth.identities (
            provider_id,
            user_id,
            identity_data,
            provider,
            last_sign_in_at,
            created_at,
            updated_at
        ) VALUES (
            '<EMAIL>',  -- provider_id should be the email for email provider
            new_user_id,
            format('{"sub": "%s", "email": "%s"}', new_user_id::text, '<EMAIL>')::jsonb,
            'email',
            now(),
            now(),
            now()
        );
        
        RAISE NOTICE 'Admin user created successfully with email: <EMAIL>';
    ELSE
        RAISE NOTICE 'Admin user <NAME_EMAIL> already exists, skipping creation';
    END IF;
END $$;